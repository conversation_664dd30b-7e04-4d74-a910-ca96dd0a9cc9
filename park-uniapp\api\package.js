import request  from '../utils/request'

// 获取选中场库的普通套餐列表
export const getPackageList = (data) => request.post('/wx/package/list', data)

// 查询某用户在某场库的某车的未过期套餐
export const getUserPackagePlate = (data) => request.post('/wx/package/user/plate', data)

// 查询用户的普通套餐购买记录
export const getUserPackageRecordList = (data) => request.post('/wx/package/user/record/list', data)

// 创建订单
export const createOrder = (data) => request.post('/wx/package/order/create', data)

// 更新订单
export const updateOrder = (data) => request.post('/wx/package/order/update', data)

// 支付回调
export const payPackageCallBack = (data) => request.post('/wx/package/order/front/payCallback', data)

// 套餐购买前，查看车辆是否在场
export const checkCarInWarehouse = (data) => request.post('/wx/package/packageJudge', data)

// VIP用户查询所有车辆套餐
export const getVipUserPackageList = (vipType) => request.get(`/wx/package/vip/list/${vipType}`)