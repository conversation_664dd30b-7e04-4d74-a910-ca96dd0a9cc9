<template>
    <view class="package-container">
        <!-- 套餐选择头部 -->
        <view class="tab-header">
            <view class="tab-item" :class="{ active: selectedPackageType.name === '停车套餐' }" 
            @tap="selectPackageType('停车套餐')">
                <text class="tab-text">停车套餐</text>
            </view>
            <view class="tab-divider"></view>
            <view class="tab-item" :class="{ active: selectedPackageType.name === '充电套餐' }"
             @tap="selectPackageType('充电套餐')">
                <text class="tab-text">充电套餐</text>
            </view>
        </view>

        <!-- 子套餐分类 -->
        <view class="sub-type-container">
            <view 
                v-for="(item, index) in currentSubTypes" 
                :key="index"
                :class="['sub-type-item', selectedSubType.name === item.name ? 'active' : '']"
                @tap="selectSubType(item)"
            >
                {{ item.name }}
            </view>
        </view>

        <!-- 套餐内容区域 -->
        <view class="package-content">
            <!-- 停车套餐组件 -->
            <ParkingNormalPackage 
                v-if="selectedPackageType.name === '停车套餐' && selectedSubType.name === '普通套餐'" 
                :key="componentKey"
                ref="parkingNormalRef"
            />
            <ParkingVipPackage 
                v-if="selectedPackageType.name === '停车套餐' && (selectedSubType.name === 'VIP套餐' || selectedSubType.name === '集团套餐')" 
                :key="componentKey"
                :userType="selectedSubType.name === '集团套餐' ? '集团套餐' : 'VIP套餐'"
                ref="parkingVipRef"
            />

            <!-- 充电套餐组件 -->
            <ChargingFastPackage 
                v-if="selectedPackageType.name === '充电套餐'" 
                :key="componentKey"
                ref="chargingFastRef"
            />
        </view>
            
        <custom-tab-bar></custom-tab-bar>
    </view>
</template>

<script setup>
import CustomTabBar from "@/components/custom-tab-bar/index.vue";
import ParkingNormalPackage from "./components/ParkingNormalPackage.vue";
import ChargingFastPackage from "./components/ChargingFastPackage.vue";
import ParkingVipPackage from "./components/ParkingVipPackage.vue";
import { ref, computed, nextTick } from 'vue';
import { onShow} from '@dcloudio/uni-app';
import { getSpecialUser } from '@/api/specialUser';

// 停车会员(默认空对象/VIP客户/集团客户)
const specialUser = ref({});

// 停车套餐子类型 - 根据用户类型动态设置
const parkingSubTypes = ref([
    { name: '普通套餐' }
]);

// 充电套餐子类型
const chargingSubTypes = ref([]);

// 当前选中的套餐类型（默认值）
const selectedPackageType = ref({ name: '停车套餐' });
const selectedSubType = ref({ name: '普通套餐' });

// 组件key，渲染子页面
const componentKey = ref(0);

// 组件引用
const parkingNormalRef = ref(null);
const parkingVipRef = ref(null);
const chargingFastRef = ref(null);

onShow(() => {
    // 首先从缓存中加载选中的套餐类型和子类型
    loadSelectedTypesFromCache();

    // 判断当前用户是否是特殊会员
    getSpecialUser().then(res => {
        specialUser.value = res.data || {};

        // 根据用户类型动态设置停车套餐选项
        if (res.data && res.data.userType) {
            if (res.data.userType === 'VIP客户') {
                parkingSubTypes.value = [
                    { name: 'VIP套餐' },
                    { name: '普通套餐' }
                ];
            } else if (res.data.userType === '集团客户') {
                parkingSubTypes.value = [
                    { name: '集团套餐' },
                    { name: '普通套餐' }
                ];
            } else {
                parkingSubTypes.value = [
                    { name: '普通套餐' }
                ];
            }
        } else {
            parkingSubTypes.value = [
                { name: '普通套餐' }
            ];
        }

        // 验证并修正缓存中选中的套餐类型和子类型是否符合当前用户权限
        validateAndCorrectSelectedTypes();

        // 初始化时触发组件重新加载
        forceComponentReload();
    });
});
// 从缓存中读取选中的套餐类型和子类型
const loadSelectedTypesFromCache = () => {
    try {
        const cachedPackageType = uni.getStorageSync('selectedPackageType');
        const cachedSubType = uni.getStorageSync('selectedSubType');
        
        if (cachedPackageType) {
            selectedPackageType.value = cachedPackageType;
        }
        if (cachedSubType) {
            selectedSubType.value = cachedSubType;
        }
    } catch (error) {
        console.log('读取缓存失败:', error);
    }
};

// 验证并修正选中的套餐类型是否符合当前用户权限
const validateAndCorrectSelectedTypes = () => {
    // 如果当前选择的是停车套餐，需要验证子类型权限
    if (selectedPackageType.value.name === '停车套餐') {
        const currentSubTypeNames = parkingSubTypes.value.map(item => item.name);
        
        // 如果当前选中的子类型不在允许的选项中，重置为第一个选项
        if (!currentSubTypeNames.includes(selectedSubType.value.name)) {
            selectedSubType.value = parkingSubTypes.value[0];
            saveSelectedTypesToCache(); 
        }
    }
};

// 保存选中的套餐类型和子类型到缓存
const saveSelectedTypesToCache = () => {
    try {
        uni.setStorageSync('selectedPackageType', selectedPackageType.value);
        uni.setStorageSync('selectedSubType', selectedSubType.value);
    } catch (error) {
        console.log('保存缓存失败:', error);
    }
};

// 渲染组件触发页面加载
const forceComponentReload = () => {
    componentKey.value++;
    // 等待DOM更新后触发子组件的数据重新加载
    nextTick(() => {
        // 增加重试机制，确保组件完全加载
        const tryInitData = (retryCount = 0) => {
            const currentRef = getCurrentComponentRef();
            
            if (currentRef && currentRef.initData) {
                currentRef.initData();
            } else if (retryCount < 5) {
                // 如果组件还没有准备好，等待更长时间后重试
                setTimeout(() => {
                    tryInitData(retryCount + 1);
                }, 200 * (retryCount + 1)); // 递增延迟时间
            }
        };
        
        // 初始延迟
        setTimeout(() => {
            tryInitData();
        }, 150);
    });
};

// 获取当前显示的组件引用
const getCurrentComponentRef = () => {
    if (selectedPackageType.value.name === '停车套餐') {
        if (selectedSubType.value.name === '普通套餐') {
            return parkingNormalRef.value;
        } else if (selectedSubType.value.name === 'VIP套餐' || selectedSubType.value.name === '集团套餐') {
            return parkingVipRef.value;
        }
    } else if (selectedPackageType.value.name === '充电套餐') {
        return chargingFastRef.value;
    }
    
    return null;
};

// 计算当前应该显示的子类型列表
const currentSubTypes = computed(() => {
    return selectedPackageType.value.name === '停车套餐' ? parkingSubTypes.value : chargingSubTypes.value;
});

// 选择套餐类型
const selectPackageType = (typeName) => {
    selectedPackageType.value = { name: typeName };
    selectedSubType.value = currentSubTypes.value[0]; 
    saveSelectedTypesToCache();
    forceComponentReload();
};

// 选择子套餐类型
const selectSubType = (subType) => {
    if(selectedSubType.value.name === subType.name) return;
    selectedSubType.value = subType;
    saveSelectedTypesToCache();
    forceComponentReload();
};

</script>

<style lang="scss" scoped>
.package-container {
    background: linear-gradient(180deg, #eaf6ff 0%, #e3edff 100%);
}

.tab-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #4BA1FC, #7e6dff);
    padding: 10rpx 30rpx;
    z-index: 10;
    flex-shrink: 0;
}

.tab-item {
    flex: 1;
    text-align: center;
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
    padding: 20rpx 0;
    position: relative;

    .tab-text {
        margin-right: 0;
    }

    &.active {
        color: #ffffff;
        font-weight: bold;

        &::after {
            content: '';
            position: absolute;
            bottom: 8rpx;
            left: 50%;
            transform: translateX(-50%);
            width: 40rpx;
            height: 4rpx;
            background-color: #ffffff;
            border-radius: 2rpx;
        }
    }
}

.tab-divider {
    width: 2rpx;
    height: 40rpx;
    background-color: rgba(255, 255, 255, 0.7);
    margin: 0 20rpx;
}

.sub-type-container {
    display: flex;
    padding: 20rpx 40rpx;
    gap: 20rpx;
}

.sub-type-item {
    padding: 14rpx 30rpx;
    text-align: center;
    background-color: #fff;
    font-size: 28rpx;
    color: #666;
    border-radius: 40rpx;

    &.active {
        background: linear-gradient(135deg, #4BA1FC, #6B73FF);
        color: #fff;
        font-weight: bold;
    }
}

</style>

