<template>
    <view class="parking-order">
        <!-- 筛选器 - 固定在顶部 -->
        <view class="filter-tabs-fixed">
            <view class="filter-tab" :class="{ active: activeFilter === 0 }" @tap="setFilter(0)">
                全部
            </view>
            <view class="filter-tab" :class="{ active: activeFilter === 1 }" @tap="setFilter(1)">
                进行中
            </view>
            <view class="filter-tab" :class="{ active: activeFilter === 5 }" @tap="setFilter(5)">
                已支付
            </view>
            <view class="filter-tab" :class="{ active: activeFilter === 4 }" @tap="setFilter(4)">
                已退款
            </view>
        </view>

        <view class="content">
            <template v-if="orderList.length > 0">
                <view class="order-list">
                    <view class="order-item" v-for="item in orderList" :key="item.id">
                        <view class="order-card">
                            <view class="card-header">
                                <view class="order-info">
                                    <text class="order-no">{{ item.tradeId || '订单号待生成' }}</text>
                                </view>
                                <view class="header-right">
                                    <view class="status-badge" :class="getStatusClass(item.payStatus)">
                                        {{ getStatusText(item.payStatus) }}
                                    </view>
                                </view>
                            </view>

                            <view class="card-body">
                                <view class="info-item">
                                    <text class="info-label">停车场:</text>
                                    <text class="info-value">{{ item.warehouseName || '未知停车场' }}</text>
                                    <text class="info-label plate-label">车牌:</text>
                                    <text class="info-value">{{ item.plateNo }}</text>
                                </view>

                                <view class="info-item">
                                    <text class="info-label">入场时间:</text>
                                    <text class="info-value time">{{ item.beginParkingTime }}</text>
                                </view>

                                <view class="info-item">
                                    <text class="info-label">出场时间:</text>
                                    <text class="info-value time">{{ item.endParkingTime }}</text>
                                </view>

                                <view class="info-item">
                                    <text class="info-label">停车时长:</text>
                                    <text class="info-value">{{ formatDuration(item.parkingDuration) }}</text>
                                </view>

                                <view class="info-item">
                                    <text class="info-label">支付时间:</text>
                                    <text class="info-value time">{{ item.paymentTime }}</text>
                                </view>

                                <view class="card-bottom">
                                    <view class="price-info">
                                        <view class="price-item" v-if="item.discountAmount > 0">
                                            <text class="price-label">优惠:</text>
                                            <text class="price-value discount">-¥{{ item.discountAmount }}</text>
                                        </view>
                                        <view class="price-item">
                                            <text class="price-label">实付:</text>
                                            <text class="price-value actual">¥{{ item.actualPayment }}</text>
                                        </view>
                                    </view>

                                    <!-- 按钮区域 -->
                                    <!-- 进行中订单 - 去支付按钮 -->
                                    <template v-if="item.payStatus === 1">
                                        <view class="invoice-actions">
                                            <view class="invoice-btn pay-btn" @tap="goToPay(item)">
                                                <up-icon name="rmb-circle-fill" color="#ffffff" size="14"></up-icon>
                                                <text>去支付</text>
                                            </view>
                                        </view>
                                    </template>

                                    <!-- 已支付订单 - 开发票按钮区域 -->
                                    <template v-if="item.payStatus === 5">
                                        <view class="invoice-actions">
                                            <!-- 已开具发票，可发送邮箱 -->
                                            <template
                                                v-if="item.miniInvoiceRecord && item.miniInvoiceRecord.status === 'ISSUED'">
                                                <view class="invoice-btn send-btn"
                                                    @tap="chooseEmail(item.miniInvoiceRecord.id)">
                                                    <up-icon name="email" color="#3b82f6" size="14"></up-icon>
                                                    <text>发送邮箱</text>
                                                </view>
                                            </template>

                                            <!-- 未开具发票，可开发票 -->
                                            <template
                                                v-if="!item.miniInvoiceRecord || (item.miniInvoiceRecord && (item.miniInvoiceRecord.status === 'UNISSU'
                                                 || item.miniInvoiceRecord.status === 'CLOSED') && !item.miniInvoiceRecord.reopenSign)">
                                                <view class="invoice-btn open-btn" @tap="routeToInvoice(item)">
                                                    <up-icon name="file-text" color="#ffffff" size="14"></up-icon>
                                                    <text>开发票</text>
                                                </view>
                                            </template>

                                            <!-- 申请换开 -->
                                            <template
                                                v-if="item.miniInvoiceRecord && ((item.miniInvoiceRecord.status === 'ISSUED' && !item.miniInvoiceRecord.reopenSign) 
                                                || (item.miniInvoiceRecord.status === 'CLOSED' && item.miniInvoiceRecord.reopenSign) || item.miniInvoiceRecord.status === 'REVERSED')">
                                                <view class="invoice-btn reopen-btn" @tap="routeToInvoice(item)">
                                                    <up-icon name="reload" color="#ffffff" size="14"></up-icon>
                                                    <text>申请换开</text>
                                                </view>
                                            </template>
                                        </view>
                                    </template>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </template>

            <template v-else>
                <view class="empty-state">
                    <up-empty text="暂无相关记录" color="#64748b"></up-empty>
                </view>
            </template>
        </view>

        <!-- 邮箱发送弹窗 -->
        <up-popup :show="showPopup" mode="center" :round="10" :safeAreaInsetBottom="false" closeOnClickOverlay
            @close="showPopup = false">
            <view class="popup-cell">
                <view class="email">
                    <view class="content_item">
                        <view class="email-title">电子邮箱</view>
                        <view class="email-input">
                            <up-input v-model="notifyEmail" border="none" placeholder="请输入您的电子邮箱" clearable
                                fontSize="28rpx" color="#616161" :placeholderStyle="placeholderStyle"
                                :customStyle="emailInputStyle"></up-input>
                        </view>
                    </view>
                    <view class="desc">如无特殊情况，我们将于24小时之内将发票发送至您的邮箱。</view>
                </view>
                <view class="choose_btn">
                    <view class="cancel_btn" @tap="showPopup = false">取消</view>
                    <view class="sure_btn" @tap="handlePostInvoiceSend">确认</view>
                </view>
            </view>
        </up-popup>
    </view>
</template>

<script setup>
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { getParkingOrderList } from '@/api/parkingOrder'
import { postInvoiceSend } from '@/api/invoice'

const orderList = ref([])
const activeFilter = ref(0) // 当前选中的筛选条件，0代表全部
const showPopup = ref(false)
const invoiceId = ref(null)
const notifyEmail = ref('')

// 样式对象
const placeholderStyle = {
    color: '#616161'
}

const emailInputStyle = {
    paddingLeft: '20rpx',
    paddingTop: '26rpx',
    paddingBottom: '26rpx',
    borderBottom: '1rpx solid rgba(189,189,189,0.2)'
}

onShow(() => {
    getOrderList(0)
})

// 设置筛选条件 - 后端查询
const setFilter = async (payStatus) => {
    if (activeFilter.value === payStatus) return // 避免重复请求
    
    activeFilter.value = payStatus
    await getOrderList(payStatus)
}

// 获取停车订单列表 - 后端查询模式
const getOrderList = async (payStatus) => {
    try {
        uni.showLoading({
            title: '加载中...',
            mask: true
        })
        
        // 构建查询参数
        const params = {}
        if (payStatus !== null) {
            params.payStatus = payStatus
        }
        
        const res = await getParkingOrderList(params)
        console.log('停车订单数据:', res)
        orderList.value = res.data || []
        
    } catch (error) {
        console.error('获取停车订单失败:', error)
        uni.showToast({
            title: '获取订单失败',
            icon: 'none'
        })
    } finally {
        uni.hideLoading()
    }
}

// 格式化停车时长（分钟转换为小时分钟）
const formatDuration = (minutes) => {
    if (!minutes) return '0分钟'
    
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours > 0) {
        return mins > 0 ? `${hours}小时${mins}分钟` : `${hours}小时`
    } else {
        return `${mins}分钟`
    }
}

// 获取支付状态对应的文本
const getStatusText = (status) => {
    switch (status) {
        case 1:
            return '进行中'
        case 4:
            return '已退款'
        case 5:
            return '已支付'
        default:
            return '未知状态'
    }
}

// 获取支付状态对应的样式类
const getStatusClass = (status) => {
    switch (status) {
        case 1:
            return 'status-progress'
        case 4:
            return 'status-failed'
        case 5:
            return 'status-paid'
        default:
            return 'status-unknown'
    }
}

// 跳转到支付页面
const goToPay = (item) => {
    // 跳转到支付详情页
    uni.navigateTo({
        url: `/pages/payPlateDetail/payPlateDetail?plateNo=${item.plateNo}&warehouseId=${item.warehouseId}`
    })
}

// 跳转到开发票页面
const routeToInvoice = (item) => {
    // functionType 功能类型：1停车，2会员
    if (item.miniInvoiceRecord && item.miniInvoiceRecord.id) {
        // 发票重开
        if (item.miniInvoiceRecord.isResume) {
            uni.navigateTo({
                url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=1&invoiceId=${item.miniInvoiceRecord.id}&isResume=${item.miniInvoiceRecord.isResume}`
            })
        } else {
            uni.navigateTo({
                url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=1&invoiceId=${item.miniInvoiceRecord.id}`
            })
        }
    } else {
        uni.navigateTo({
            url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=1`
        })
    }
}

// 选择邮箱发送
const chooseEmail = (id) => {
    invoiceId.value = id
    showPopup.value = true
}

// 发送发票到邮箱
const handlePostInvoiceSend = async () => {
    if (!notifyEmail.value) {
        uni.showToast({
            title: '请输入邮箱',
            icon: 'none'
        })
        return
    }
    
    try {
        const params = {
            id: invoiceId.value,
            notifyEmail: notifyEmail.value
        }
        
        await postInvoiceSend(params)
        
        uni.showToast({
            title: '邮箱发送成功～',
            icon: 'none',
            duration: 2000
        })
        
        setTimeout(() => {
            showPopup.value = false
            notifyEmail.value = ''
        }, 1000)
    } catch (error) {
        console.error('发送邮箱失败:', error)
        uni.showToast({
            title: '发送失败，请重试',
            icon: 'none'
        })
    }
}
</script>

<style lang="scss" scoped>
.parking-order {
    min-height: 100vh;
    background-color: #f5f5f5;
}

.filter-tabs-fixed {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: #ffffff;
    z-index: 10;
    padding: 20rpx 0;
    box-shadow: 0 4rpx 12rpx rgba(36, 107, 253, 0.08);
    
    display: flex;
    border-radius: 0;
    
    .filter-tab {
        flex: 1;
        text-align: center;
        padding: 16rpx 12rpx;
        font-size: 26rpx;
        color: #666666;
        border-radius: 12rpx;
        transition: all 0.3s ease;
        
        &.active {
            background: #3b82f6;
            color: white;
            font-weight: 500;
        }
        
        &:not(.active):active {
            background: #f8fafc;
        }
    }
}

.content {
    padding: 120rpx 24rpx 40rpx; /* 顶部增加padding避免被固定筛选器遮挡 */
}

.order-list {
    .order-item {
        margin-bottom: 20rpx;
        
        .order-card {
            background: white;
            border-radius: 20rpx;
            padding: 32rpx;
            box-shadow: 0 8rpx 24rpx rgba(36, 107, 253, 0.1);
            
            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16rpx;
                
                .order-info {
                    .order-no {
                        font-size: 20rpx;
                        font-weight: bold;
                        color: #797979;
                    }
                }
                
                .header-right {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;
                    
                    .status-badge {
                        padding: 6rpx 12rpx;
                        border-radius: 16rpx;
                        font-size: 22rpx;
                        font-weight: 500;
                        
                        &.status-progress {
                            background: #f0fdf4;
                            color: #16a34a;
                            border: 1rpx solid #bbf7d0;
                        }
                        
                        &.status-failed {
                            background: #fef2f2;
                            color: #dc2626;
                            border: 1rpx solid #fecaca;
                        }
                        
                        &.status-cancelled {
                            background: #fef3c7;
                            color: #d97706;
                            border: 1rpx solid #fed7aa;
                        }
                        
                        &.status-paid {
                            background: #f0f9ff;
                            color: #0ea5e9;
                            border: 1rpx solid #bae6fd;
                        }
                        
                        &.status-unknown {
                            background: #f9fafb;
                            color: #6b7280;
                            border: 1rpx solid #e5e7eb;
                        }
                    }
                }
            }
            
            .card-body {
                .info-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 16rpx;
                    
                    .info-label {
                        font-size: 26rpx;
                        color: #666666;
                        margin-right: 16rpx;
                        min-width: 120rpx;
                        
                        &.plate-label {
                            margin-left: 40rpx;
                            min-width: 80rpx;
                        }
                    }
                    
                    .info-value {
                        font-size: 26rpx;
                        color: #333333;
                        flex: 1;
                        
                        &.time {
                            color: #333333;
                        }
                    }
                }
                
                .card-bottom {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding-top: 10rpx;
                    border-top: 1rpx solid #f5f5f5;
                    
                    .price-info {
                        display: flex;
                        align-items: center;
                        
                        .price-item {
                            display: flex;
                            align-items: center;
                            margin-right: 20rpx;
                            
                            .price-label {
                                font-size: 22rpx;
                                color: #666666;
                                margin-right: 8rpx;
                            }
                            
                            .price-value {
                                font-size: 26rpx;
                                font-weight: bold;
                                
                                &.discount {
                                    color: #16a34a;
                                }
                                
                                &.actual {
                                    color: #ff0000;
                                    font-size: 30rpx;
                                }
                            }
                        }
                    }
                    
                    .invoice-actions {
                        display: flex;
                        align-items: center;
                        
                        .invoice-btn {
                            display: flex;
                            align-items: center;
                            padding: 12rpx 16rpx;
                            border-radius: 8rpx;
                            font-size: 24rpx;
                            font-weight: 500;
                            margin-left: 10rpx;
                            
                            text {
                                margin-left: 6rpx;
                            }
                            
                            &.send-btn {
                                background: #f0f9ff;
                                color: #3b82f6;
                                border: 1rpx solid #bae6fd;
                            }
                            
                            &.open-btn {
                                background: #3b82f6;
                                color: #ffffff;
                            }
                            
                            &.reopen-btn {
                                background: #f59e0b;
                                color: #ffffff;
                            }
                            
                            &.pay-btn {
                                background: #16a34a;
                                color: #ffffff;
                            }
                        }
                    }
                }
            }
        }
    }
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 160rpx 40rpx;
    margin-top: 60rpx;
    
    .empty-text {
        font-size: 28rpx;
        color: #64748b;
        font-weight: 500;
        margin-top: 32rpx;
    }
}

.popup-cell {
    width: 600rpx;
    text-align: center;
    padding: 44rpx 0;
    
    .email {
        padding: 32rpx;
        background: #ffffff;
        border-radius: 20rpx;
        
        .content_item {
            margin-bottom: 20rpx;
            
            .email-title {
                font-size: 28rpx;
                color: #212121;
                font-weight: 500;
                margin-bottom: 16rpx;
                text-align: left;
            }
        }
        
        .desc {
            font-size: 24rpx;
            color: #f5820e;
            line-height: 1.5;
            margin-top: 16rpx;
        }
    }
    
    .choose_btn {
        display: flex;
        justify-content: space-around;
        padding: 0 20rpx;
        margin-top: 32rpx;
        
        .cancel_btn, .sure_btn {
            width: 240rpx;
            height: 80rpx;
            line-height: 80rpx;
            border-radius: 80rpx;
            text-align: center;
            font-size: 28rpx;
            font-weight: 600;
        }
        
        .cancel_btn {
            background: #ffffff;
            border: 2rpx solid #246bfd;
            color: #246bfd;
        }
        
        .sure_btn {
            background: #246bfd;
            color: #ffffff;
        }
    }
}
</style>