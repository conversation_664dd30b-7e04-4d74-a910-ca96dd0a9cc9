import { URL } from '@/config/index.js'

/**
 * 提示方法
 * @param {String} title 提示文字
 * @param {String}  icon icon图片
 * @param {Number}  duration 提示时间
 */
function toast(title, icon = 'none', duration = 2000) {
  if (title) {
    uni.showToast({
      title,
      icon,
      duration
    })
  }
}

/**
 * 设置缓存
 * @param {String} key 键名
 * @param {String} data 值
 */
export function setStorageSync(key, data) {
  uni.setStorageSync(key, data)
}

/**
 * 获取缓存
 * @param {String} key 键名
 */
export function getStorageSync(key) {
  return uni.getStorageSync(key)
}

/**
 * 删除缓存
 * @param {String} key 键名
 */
export function removeStorageSync(key) {
  return uni.removeStorageSync(key)
}

/**
 * 清空缓存
 * @param {String} key 键名
 */
export function clearStorageSync() {
  return uni.clearStorageSync()
}

/**
 * 深拷贝
 * @param {Object} data
 **/
const deepClone = data => JSON.parse(JSON.stringify(data))

function DateInitFromStr(inStr) {
  // 这个函数是支持你现在提供的2种数据格式的，包括了'2020-07-08 13:24:27'和'2020-07-01'
  // 对于'2020-07-01'，等效于'2020-07-01 00:00:00.000'
  return new Date(inStr)
}
function getTimeDiff(inDStr1, inDStr2) {
  let D1 = DateInitFromStr(inDStr1)
  let D2 = DateInitFromStr(inDStr2)
  return (D1.getTime() - D2.getTime()) / 1000
}

export default {
  toast,
  deepClone,
  getTimeDiff
}

// 获取微信导航栏信息
export const getWechatNavBarInfo = () => {
  const systemInfo = uni.getSystemInfoSync();
  const menuButton = wx.getMenuButtonBoundingClientRect();
  
  // 关键计算公式（微信小程序专用）
  return {
    statusBarHeight: systemInfo.statusBarHeight, // 状态栏高度
    navBarHeight: menuButton.bottom + menuButton.top - systemInfo.statusBarHeight, // 导航栏总高度
    menuButtonRight: systemInfo.windowWidth - menuButton.right, // 胶囊按钮右侧距离
    warehouseTop: menuButton.top + (menuButton.height - 30.5) / 2, // 30.5为选择器高度
    warehouseLeft: 8, // 与胶囊按钮左对齐间距
    windowHeightHalf: systemInfo.windowHeight / 2, // 窗口宽度一半
  };
};

// 跨平台兼容的日期比较函数
export const isSameDate = (date1, date2) => {
    if (!date1 || !date2) return false;
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
};

// 安全提取日期部分（YYYY-MM-DD格式）
export const extractDatePart = (dateTimeString) => {
    if (!dateTimeString) return '';
    
    try {
        // 尝试多种方式提取日期部分
        if (dateTimeString.includes(' ')) {
            return dateTimeString.split(' ')[0];
        } else if (dateTimeString.includes('T')) {
            return dateTimeString.split('T')[0];
        } else {
            // 如果是纯日期格式，验证格式后返回
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (dateRegex.test(dateTimeString)) {
                return dateTimeString;
            } else {
                console.warn('日期格式不正确:', dateTimeString);
                return '';
            }
        }
    } catch (error) {
        console.error('提取日期部分失败:', error, dateTimeString);
        return '';
    }
};

// 格式化日期为YYYY-MM-DD格式（跨平台兼容）
export const formatDate = (date) => {
    if (!date) return '';
    
    try {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        return `${year}-${month}-${day}`;
    } catch (error) {
        console.error('格式化日期失败:', error, date);
        return '';
    }
};

// 安全创建日期对象（避免平台差异）
export const createDate = (dateString) => {
    if (!dateString) return new Date();
    
    try {
        // 如果是 YYYY-MM-DD 格式，确保正确解析
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
            const [year, month, day] = dateString.split('-');
            return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
        } else {
            // 其他格式使用默认构造函数
            return new Date(dateString);
        }
    } catch (error) {
        console.error('创建日期对象失败:', error, dateString);
        return new Date();
    }
};

// 上传头像文件
export const uploadAvatar = (filePath) => {
    return new Promise((resolve, reject) => {
        uni.uploadFile({
            url: URL+'/file/upload/path',
            filePath: filePath,
            name: 'file',
            formData: {
                path: 'avatar'
            },
            header: {
                'Authorization': 'WxBearer ' + (uni.getStorageSync('token') || '')
            },
            success: (uploadRes) => {
                try {
                    const result = JSON.parse(uploadRes.data);
                    if (result.code === 200) {
                        resolve(result.data.url);
                    } else {
                        reject(new Error(result.msg || '上传失败'));
                    }
                } catch (error) {
                    reject(new Error('解析响应失败'));
                }
            },
            fail: (error) => {
                reject(error);
            }
        });
    });
};