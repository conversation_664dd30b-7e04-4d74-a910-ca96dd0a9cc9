package com.lgjy.gate.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.domain.R;
import com.lgjy.gate.annotation.GateService;
import com.lgjy.gate.config.BrandConfigManager;
import com.lgjy.gate.config.RetryRestTemplate;
import com.lgjy.gate.mapper.GateLogMapper;
import com.lgjy.gate.pojo.GateLogPojo;
import com.lgjy.gate.service.GateControlService;
import com.lgjy.gate.utils.SiZhuoUtils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;

import java.util.Optional;
import java.util.UUID;

@Slf4j
@GateService(brandCode = "sizhuo", serviceType = GateService.Type.CONTROL, description = "思卓停车场控制服务")
public class SiZhuoControlServiceImpl implements GateControlService {

    private final RestTemplate restTemplate = RetryRestTemplate.build(3, 100, 5000, 5000);

    @Resource
    private SiZhuoUtils siZhuoUtils;

    @Resource
    private GateLogMapper gateLogMapper;

    @Resource
    private BrandConfigManager brandConfigManager;

    /**
     * 停车订单支付通知道闸平台
     *
     * @param requestParams 请求数据JSON对象
     * @return
     */
    @Override
    public R<String> payOrder(JSONObject requestParams) {
        // 从JSONObject中提取参数
        BigDecimal paymentAmount = requestParams.getBigDecimal("paymentAmount");
        String parkingId = requestParams.getString("parkingId");
        int payType = requestParams.getIntValue("payType");
        String plateNo = requestParams.getString("plateNo");
        String tradeId = requestParams.getString("tradeId");
        String remoteAddr = requestParams.getString("remoteAddr");

        // 获取可选参数
        Integer outChannelIndex = requestParams.containsKey("gateNo") ? requestParams.getIntValue("gateNo") : null;
        Integer isNoPlate = requestParams.containsKey("isNoPlate") ? requestParams.getIntValue("isNoPlate") : null;

        // 获取配置信息
        String physicalId = siZhuoUtils.getPhysicalId(parkingId);
        String brandName = siZhuoUtils.getSiZhuoParkConfig().getBrandName();
        String baseUrl = siZhuoUtils.getSiZhuoParkConfig().getRequestUrl();
        String requestUrl = baseUrl + "/transmit/pay/notice";

        // 生成签名
        long timestamp = System.currentTimeMillis();
        int amount = paymentAmount.setScale(2, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100")).intValue();

        // 构建签名参数（按字母顺序）
        StringBuilder signDataBuilder = new StringBuilder();
        signDataBuilder.append("amount=").append(amount);

        // 如果是无牌车，加入签名
        if (outChannelIndex != null && outChannelIndex != 0) {
            signDataBuilder.append("&isNoPlate=").append(isNoPlate)
                    .append("&outChannelIndex=").append(outChannelIndex);
        }

        signDataBuilder.append("&parkingID=").append(physicalId)
                .append("&payType=").append(payType)
                .append("&plate=").append(plateNo)
                .append("&timestamp=").append(timestamp)
                .append("&tradeNo=").append(tradeId);

        String sign = siZhuoUtils.getSignData(physicalId, signDataBuilder);

        // 构建请求数据
        JSONObject requestData = new JSONObject();
        requestData.put("amount", amount);

        // 如果存在outChannelIndex，加入请求数据
        if (outChannelIndex != null && outChannelIndex != 0) {
            requestData.put("outChannelIndex", outChannelIndex);
            requestData.put("isNoPlate", isNoPlate);
        }

        requestData.put("parkingID", physicalId);
        requestData.put("payType", payType);
        requestData.put("plate", plateNo);
        requestData.put("timestamp", timestamp);
        requestData.put("tradeNo", tradeId);
        requestData.put("sign", sign);

        // 发送HTTP请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(requestData, headers);
        JSONObject response = restTemplate.postForObject(requestUrl, httpEntity, JSONObject.class);

        log.info("payOrder: 请求路径{},请求数据{},返回数据{}:", requestUrl,requestData,response);
        recordOperationLog(parkingId, brandName, plateNo, remoteAddr, requestUrl,
                requestData, response);

        if (response.getInteger("code") == 1) {
            return R.ok();
        } else {
            return R.fail(response.getString("msg"));
        }
    }

    /**
     * 无牌车入场
     *
     * @param requestParams 请求数据JSON对象
     * @return
     */
    @Override
    public R<String> noPlateIn(JSONObject requestParams) {
        String parkingId = requestParams.getString("parkingId");
        int channelIndex = requestParams.getIntValue("gateNo");
        String plate = requestParams.getString("plateNo");
        String remoteAddr = requestParams.getString("remoteAddr");
        // 获取配置信息
        String physicalId = brandConfigManager.getPhysicalId(parkingId);
        String brandName = brandConfigManager.getSiZhuoParkConfig().getBrandName();
        String baseUrl = brandConfigManager.getSiZhuoParkConfig().getRequestUrl();
        String requestUrl = baseUrl + "/transmit/noplate/entry";

        long timestamp = System.currentTimeMillis();
        // 生成签名
        StringBuilder signDataBuilder = new StringBuilder();
        signDataBuilder.append("channelIndex=").append(channelIndex)
                .append("&parkingID=").append(physicalId)
                .append("&plate=").append(plate)
                .append("&timestamp=").append(timestamp);
        String sign = siZhuoUtils.getSignData(physicalId, signDataBuilder);

        // 准备请求数据
        JSONObject requestData = new JSONObject();
        requestData.put("timestamp", timestamp);
        requestData.put("parkingID", physicalId);
        requestData.put("plate", plate);
        requestData.put("channelIndex", channelIndex);
        requestData.put("sign", sign);

        // 发送HTTP请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(requestData, headers);
        JSONObject response = restTemplate.postForObject(requestUrl, httpEntity, JSONObject.class);
        log.info("noPlateIn:"+ requestUrl + requestData+ response.toJSONString());
        recordOperationLog(parkingId, brandName, plate, remoteAddr, requestUrl,
                requestData, response);
        if (response.get("code").equals(1)) {
            return R.ok();
        } else {
            return R.fail(response.getString("msg"));
        }
    }

    /**
     * 增加月租车
     *
     * @param requestParams 请求数据JSON对象
     * @return
     */
    @Override
    public R<String> saveMonthCar(JSONObject requestParams) {
        // 从JSONObject中提取参数
        String parkingId = requestParams.getString("parkingId");
        String plateNum = requestParams.getString("plateNum");
        String userId = requestParams.getString("userId");
        Date beginDate = requestParams.getDate("beginDate");
        Date endDate = requestParams.getDate("endDate");
        String remoteAddr = requestParams.getString("remoteAddr");
        // 获取配置信息
        String physicalId = siZhuoUtils.getPhysicalId(parkingId);
        String brandName = siZhuoUtils.getSiZhuoParkConfig().getBrandName();
        String baseUrl = siZhuoUtils.getSiZhuoParkConfig().getRequestUrl();
        String requestUrl = baseUrl + "/transmit/vehicle/save";

        // 准备请求数据
        long timestamp = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        JSONObject requestData = new JSONObject();
        requestData.put("timestamp", timestamp);
        requestData.put("plate", plateNum);
        requestData.put("vehicleType", "月租车");
        requestData.put("beginTime", sdf.format(beginDate) + " 00:00:00");
        requestData.put("endTime", sdf.format(endDate) + " 23:59:59");
        requestData.put("userName", userId);
        requestData.put("parkingID", physicalId);
        StringBuilder signDataBuilder = new StringBuilder();
        signDataBuilder.append("beginTime=").append(requestData.get("beginTime"))
                .append("&endTime=").append(requestData.get("endTime"))
                .append("&parkingID=").append(physicalId)
                .append("&plate=").append(requestData.get("plate"))
                .append("&timestamp=").append(requestData.get("timestamp"))
                .append("&userName=").append(requestData.get("userName"))
                .append("&vehicleType=").append(requestData.get("vehicleType"));
        String sign = siZhuoUtils.getSignData(physicalId, signDataBuilder);
        requestData.put("sign", sign);

        // 请求头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(requestData, headers);
        JSONObject response = restTemplate.postForObject(requestUrl, httpEntity, JSONObject.class);
        log.info("saveMonthCar:"+ requestUrl + requestData+ response.toJSONString());
        recordOperationLog(parkingId, brandName, plateNum, remoteAddr, requestUrl,
                requestData, response);

        if (response.get("code").equals(1)) {
            return R.ok();
        } else {
            return R.fail(response.getString("msg"));
        }
    }

    /**
     * 删除月租车
     *
     * @param requestParams 请求数据JSON对象
     * @return
     */
    @Override
    public R<String> delMonthCar(JSONObject requestParams) {
        // 从JSONObject中提取参数
        String parkingId = requestParams.getString("parkingId");
        String plateNum = requestParams.getString("plateNum");
        String remoteAddr = requestParams.getString("remoteAddr");
        // 获取配置信息
        String physicalId = siZhuoUtils.getPhysicalId(parkingId);
        String brandName = siZhuoUtils.getSiZhuoParkConfig().getBrandName();
        String baseUrl = siZhuoUtils.getSiZhuoParkConfig().getRequestUrl();
        String requestUrl = baseUrl + "/transmit/vehicle/delete";

        // 准备签名
        long timestamp = System.currentTimeMillis();
        StringBuilder signDataBuilder = new StringBuilder();
        signDataBuilder.append("parkingID=").append(physicalId).append("&plate=").append(plateNum)
                .append("&timestamp=").append(timestamp);
        String sign = siZhuoUtils.getSignData(physicalId, signDataBuilder);
        // 准备请求数据
        JSONObject requestData = new JSONObject();
        requestData.put("timestamp", timestamp);
        requestData.put("plate", plateNum);
        requestData.put("parkingID", physicalId);
        requestData.put("sign", sign);

        // 请求头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(requestData, headers);
        JSONObject response = restTemplate.postForObject(requestUrl, httpEntity, JSONObject.class);
        log.info("delMonthCar:" + response.toJSONString());
        // 记录请求日志
        recordOperationLog(parkingId, brandName, plateNum, remoteAddr, requestUrl,
                requestData, response);
        if (response.get("code").equals(1)) {
            return R.ok();
        } else {
            return R.fail(response.getString("msg") == null ? "" : response.getString("msg"));
        }
    }

    /**
     * 添加黑名单车辆
     *
     * @param requestParams 请求数据JSON对象
     * @return
     */
    @Override
    public R<String> saveBlackCar(JSONObject requestParams) {

        // 从JSONObject中提取参数
        String parkingId = requestParams.getString("parkingId");
        String plateNum = requestParams.getString("plateNum");
        Date endDate = requestParams.getDate("endDate");
        String remoteAddr = requestParams.getString("remoteAddr");
        // 获取配置信息
        String physicalId = siZhuoUtils.getPhysicalId(parkingId);
        String brandName = siZhuoUtils.getSiZhuoParkConfig().getBrandName();
        String baseUrl = siZhuoUtils.getSiZhuoParkConfig().getRequestUrl();
        String requestUrl = baseUrl + "/transmit/blacklist/save";

        // 准备签名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        long timestamp = System.currentTimeMillis();
        String endTime = sdf.format(endDate) + " 23:59:59";
        StringBuilder signDataBuilder = new StringBuilder();
        signDataBuilder.append("endTime=").append(endTime)
                .append("&parkingID=").append(physicalId)
                .append("&plate=").append(plateNum)
                .append("&timestamp=").append(timestamp);
        String sign = siZhuoUtils.getSignData(physicalId, signDataBuilder);
        // 准备请求数据
        JSONObject requestData = new JSONObject();
        requestData.put("timestamp", timestamp);
        requestData.put("plate", plateNum);
        requestData.put("parkingID", physicalId);
        requestData.put("endTime", endTime);
        requestData.put("sign", sign);

        // 请求头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(requestData, headers);
        JSONObject response = restTemplate.postForObject(requestUrl, httpEntity, JSONObject.class);
        log.info("saveBlackCar:" + response.toJSONString());
        log.info("saveBlackCar:" + requestUrl + requestData + response.toJSONString());
        // 记录请求日志
        recordOperationLog(parkingId, brandName, plateNum, remoteAddr, requestUrl,
                requestData, response);
        if (response.get("code").equals(1)) {
            return R.ok();
        } else {
            return R.fail(response.getString("msg") == null ? "" : response.getString("msg"));
        }
    }

    /**
     * 删除黑名单车辆
     *
     * @param requestParams 请求数据JSON对象
     * @return
     */
    @Override
    public R<String> delBlackCar(JSONObject requestParams) {
        // 从JSONObject中提取参数
        String parkingId = requestParams.getString("parkingId");
        String plateNum = requestParams.getString("plateNum");
        String remoteAddr = requestParams.getString("remoteAddr");
        // 获取配置信息
        String physicalId = siZhuoUtils.getPhysicalId(parkingId);
        String brandName = siZhuoUtils.getSiZhuoParkConfig().getBrandName();
        String baseUrl = siZhuoUtils.getSiZhuoParkConfig().getRequestUrl();
        String requestUrl = baseUrl + "/transmit/blacklist/delete";

        // 准备签名
        long timestamp = System.currentTimeMillis();
        StringBuilder signDataBuilder = new StringBuilder();
        signDataBuilder.append("parkingID=").append(physicalId).append("&plate=").append(plateNum)
                .append("&timestamp=").append(timestamp);
        String sign = siZhuoUtils.getSignData(physicalId, signDataBuilder);
        // 准备请求数据
        JSONObject requestData = new JSONObject();
        requestData.put("timestamp", timestamp);
        requestData.put("plate", plateNum);
        requestData.put("parkingID", physicalId);
        requestData.put("sign", sign);

        // 请求头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(requestData, headers);
        JSONObject response = restTemplate.postForObject(requestUrl, httpEntity, JSONObject.class);
        log.info("delBlackCar:" + response.toJSONString());
        // 记录请求日志
        recordOperationLog(parkingId, brandName, plateNum, remoteAddr, requestUrl,
                requestData, response);
        if (response.get("code").equals(1)) {
            return R.ok();
        } else {
            return R.fail(response.getString("msg") == null ? "" : response.getString("msg"));
        }
    }

    /**
     * 添加免费车
     *
     * @param requestParams 请求数据JSON对象
     * @return
     */
    @Override
    public R<String> saveFreeCar(JSONObject requestParams) {
        // 从JSONObject中提取参数
        String parkingId = requestParams.getString("parkingId");
        String plateNum = requestParams.getString("plateNum");
        String userName = requestParams.getString("userName");
        Date beginDate = requestParams.getDate("beginDate");
        Date endDate = requestParams.getDate("endDate");
        String remoteAddr = requestParams.getString("remoteAddr");
        // 获取配置信息
        String physicalId = siZhuoUtils.getPhysicalId(parkingId);
        String brandName = siZhuoUtils.getSiZhuoParkConfig().getBrandName();
        String baseUrl = siZhuoUtils.getSiZhuoParkConfig().getRequestUrl();
        String requestUrl = baseUrl + "/transmit/vehicle/save";

        // 准备请求数据
        long timestamp = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        JSONObject requestData = new JSONObject();
        requestData.put("timestamp", timestamp);
        requestData.put("plate", plateNum);
        requestData.put("vehicleType", "免费车");
        requestData.put("beginTime", sdf.format(beginDate) + " 00:00:00");
        requestData.put("endTime", sdf.format(endDate) + " 23:59:59");
        requestData.put("userName", userName);
        requestData.put("parkingID", physicalId);

        StringBuilder signDataBuilder = new StringBuilder();
        signDataBuilder.append("beginTime=").append(requestData.get("beginTime"))
                .append("&endTime=").append(requestData.get("endTime")).append("&parkingID=")
                .append(physicalId)
                .append("&plate=").append(requestData.get("plate")).append("&timestamp=")
                .append(requestData.get("timestamp"))
                .append("&userName=").append(requestData.get("userName")).append("&vehicleType=")
                .append(requestData.get("vehicleType"));
        String sign = siZhuoUtils.getSignData(physicalId, signDataBuilder);
        requestData.put("sign", sign);

        // 请求头信息
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(requestData, headers);
        JSONObject response = restTemplate.postForObject(requestUrl, httpEntity, JSONObject.class);
        log.info("saveFreeCar:" + response.toJSONString());
        recordOperationLog(parkingId, brandName, plateNum, remoteAddr, requestUrl,
                requestData, response);

        if (response.get("code").equals(1)) {
            return R.ok();
        } else {
            return R.fail(response.getString("msg") == null ? "" : response.getString("msg"));
        }
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(String parkingId, String brandName, String plateNum, String accessAddress,
            String url, JSONObject requestData, JSONObject response) {

        GateLogPojo logPojo = new GateLogPojo();
        logPojo.setId(UUID.randomUUID().toString().replace("-", ""));
        logPojo.setParkingName(brandName);
        logPojo.setAccessAddress(accessAddress);
        logPojo.setAddress(url);
        logPojo.setOperate(0);
        logPojo.setParm(String.format("parkingId:%s&&plateNum:%s", parkingId, plateNum));
        logPojo.setData(requestData.toJSONString());
        logPojo.setResult(response.getInteger("code"));
        logPojo.setResultMsg(Optional.ofNullable(response.getString("msg")).orElse(""));
        logPojo.setLastUpdate(new Date());

        gateLogMapper.gateLogAdd(logPojo);
    }
}
