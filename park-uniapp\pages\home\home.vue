<template>
  <view class="home-container">

    <!-- 地图容器 -->
    <view class="map-container">
      <map id="myMap" :style="{ width: '100%', height: '85%' }" :latitude="center.latitude"
        :longitude="center.longitude" :scale="scale" :markers="markers"></map>

      <!-- 场库类型切换按钮 -->
      <view class="map-controls" :style="controlsStyles">
        <view class="control-button" :class="{ active: currentType === 'parking' }" @click="switchToParking">
          <text class="button-text">停车场库</text>
        </view>
        <view class="control-button" :class="{ active: currentType === 'charging' }" @click="switchToCharging">
          <text class="button-text">充电场库</text>
        </view>
      </view>
    </view>

    <!-- 浮动遮罩层 -->
    <view class="floating-overlay" :class="overlayClasses" :style="overlayStyles">

      <!-- 拖拽手柄区域 -->
      <view class="drag-handle-container" @touchstart="handleTouchStart" @touchmove="handleTouchMove"
        @touchend="handleTouchEnd" @touchcancel="handleTouchEnd">
        <view class="drag-handle-bar"></view>
      </view>

      <!-- 功能按钮区域 -->
      <view class="buttons-container">
        <view class="action-button" @tap="handleParkingPayment">
          <image src="/static/image/parkPay.png" mode="widthFix" style="width: 100rpx; height: 100rpx;"></image>
          <text class="button-text">停车缴费</text>
        </view>
        <view class="action-button" @tap="handleChargingCode">
          <image src="/static/image/scanCharge​.png" mode="widthFix" style="width: 100rpx; height: 100rpx;"></image>
          <text class="button-text">扫码充电</text>
        </view>
      </view>

      <!-- 主要内容区域 -->
      <view class="overlay-content" :class="[isMinimized ? 'content-hidden' : '']">
        <!-- 轮播图区域 -->
        <view class="banner-section" :class="[isMinimized ? 'content-hidden' : '']">
          <up-swiper :list="advertConfigList" :autoplay="true" :circular="true" :interval="3000" :duration="500"
            height="140"></up-swiper>
        </view>
      </view>
    </view>

    <custom-tab-bar></custom-tab-bar>
  </view>
</template>

<script setup>
import CustomTabBar from "@/components/custom-tab-bar/index.vue";
import { ref, computed } from "vue";
import { onShow, onReady } from "@dcloudio/uni-app";
import { AMapWX } from "@/utils/amap.js";
import { getParkWareHouseList } from "@/api/warehouse";
import { getAdvertConfigList } from "@/api/advertConifg";


onShow(() => {
  initMap();
  initAdvertData();
  initWarehouseData();
});

onReady(() => {
  // 初始化浮动面板位置
  initializePanelPositions();
});

// 广告数据
const advertConfigList = ref([]);

// 场库类型管理
const currentType = ref('parking'); 

// 地图配置
const amap = ref(null);
const scale = ref(16);
const center = ref({ latitude: 30.88, longitude: 121.81 });
const markers = ref([]);

// 停车场库数据
const wareHouseList = ref([]);

// 充电场库示例数据
const chargingStations = ref([
  {
    id: "charging_001",
    name: "充电站A",
    latitude: 31.2304,
    longitude: 121.4737,
    type: 'charging'
  }
]);

// 初始化广告数据
const initAdvertData = async () => {
  try {
    const res = await getAdvertConfigList();
    advertConfigList.value = res.data.map(item => ({
      url: item.picUrl
    }));
  } catch (error) {
    uni.showToast({
      title: '广告数据加载失败',
      icon: 'none'
    });
  }
};

// 初始化场库数据
const initWarehouseData = async () => {
  try {
    const res = await getParkWareHouseList();
    wareHouseList.value = res.data.map(item => ({
      id: item.id,
      name: item.warehouseName,
      latitude: item.latitude,
      longitude: item.longitude
    }));

    // 更新地图标记，显示所有场库
    updateMarkers();
  } catch (error) {
    uni.showToast({
      title: '仓库数据加载失败',
      icon: 'none'
    });
  }
};

// 切换到停车场库
const switchToParking = () => {
  currentType.value = 'parking';
  updateMarkers();
};

// 切换到充电场库
const switchToCharging = () => {
  currentType.value = 'charging';
  updateMarkers();
};

// 初始化地图
const initMap = () => {
  try {
    amap.value = new AMapWX({
      key: "f361a38bbacd22fba61e2d661f977e95",
      hideCopyright: true,
    });
    checkLocationPermission();
  } catch (error) {
    uni.showToast({
      title: '地图初始化失败',
      icon: 'none'
    });
  }
};

// 检查定位权限
const checkLocationPermission = async () => {
  try {
    const res = await uni.getSetting({});
    if (!res.authSetting["scope.userLocation"]) {
      uni.showModal({
        title: "位置权限提示",
        content: "需要获取您的位置以提供地图服务",
        success: (res) => {
          if (res.confirm) {
            uni.authorize({
              scope: "scope.userLocation",
              success: () => getCurrentLocation(),
              fail: () => {
                uni.showToast({
                  title: '位置权限获取失败',
                  icon: 'none'
                });
              }
            });
          }
        },
      });
    } else {
      getCurrentLocation();
    }
  } catch (error) {
    uni.showToast({
      title: '定位权限检查失败',
      icon: 'none'
    });
  }
};

// 获取当前位置
const getCurrentLocation = () => {
  uni.getLocation({
    type: 'gcj02',
    isHighAccuracy: true,
    success: (res) => {
      center.value = {
        latitude: res.latitude,
        longitude: res.longitude
      };
      uni.setStorageSync("currentLocation", center.value);
      updateMarkers();
    },
    fail: (error) => {
      console.error('获取位置失败:', error);
      uni.showToast({
        title: "定位失败，请检查定位权限",
        icon: "none"
      });
    }
  });
};

// 更新标记
const updateMarkers = () => {
  const markersList = [];
  
  // 只有在有用户位置信息时才添加用户当前位置标记
  if (center.value.latitude && center.value.longitude) {
    markersList.push({
      id: 0, // 用户位置使用id: 0
      ...center.value,
      iconPath: "/static/image/user-location.png",
      width: 30,
      height: 30,
      callout: { 
        content: "当前位置", 
        display: 'ALWAYS',
        color: '#000000',
        bgColor: '#ffffff',
        fontSize: 12,
        fontWeight: 'bold',
        padding: 5,
        borderRadius: 12
      }
    });
  }
  
  // 根据当前类型显示对应的场库
  if (currentType.value === 'parking') {
    // 显示停车场库
    wareHouseList.value.forEach((warehouse, index) => {
      if (warehouse.latitude && warehouse.longitude) {
        markersList.push({
          id: index + 1, // 使用索引+1作为id，避免与用户位置id冲突
          latitude: warehouse.latitude,
          longitude: warehouse.longitude,
          iconPath: "/static/image/parking-location.png", // 使用停车场图标
          width: 30,
          height: 30,
          callout: { 
            content: warehouse.name, 
            display: 'ALWAYS',
            color: '#000000',
            bgColor: '#ffffff',
            fontSize: 14,
            fontWeight: 'bold',
            padding: 5,
            borderRadius: 12
          }
        });
      }
    });
  } else if (currentType.value === 'charging') {
    // 显示充电场库
    chargingStations.value.forEach((station, index) => {
      if (station.latitude && station.longitude) {
        markersList.push({
          id: index + 1, // 使用索引+1作为id，避免与用户位置id冲突
          latitude: station.latitude,
          longitude: station.longitude,
          iconPath: "/static/image/charging-location.png", // 使用充电图标
          width: 30,
          height: 30,
          callout: { 
            content: station.name, 
            display: 'ALWAYS',
            color: '#000000',
            bgColor: '#ffffff',
            fontSize: 14,
            fontWeight: 'bold',
            padding: 5,
            borderRadius: 12
          }
        });
      }
    });
  }
  
  markers.value = markersList;
}

// -------------------- 浮动面板状态管理 --------------------
const overlayClasses = computed(() => ({
  'overlay-dragging': isDragging.value
}))

const overlayStyles = computed(() => ({
  transform: `translateY(${panelPosition.value}px)`,
  transition: isDragging.value ? 'none' : 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  height: `${panelHeight.value}px`,
  paddingBottom: `${tabBarHeight.value}px` // 内部为底部导航栏留出空间
}))

const controlsStyles = computed(() => {
  // 切换按钮的位置：面板高度 + 导航栏高度 + 按钮与面板的间距 - 拖拽偏移
  const controlsBottom = panelHeight.value + tabBarHeight.value + 10 - panelPosition.value;
  
  return {
    bottom: `${controlsBottom}px`
  };
})

const panelPosition = ref(0);
const isDragging = ref(false);
const startY = ref(0);
const startPosition = ref(0);
const isMinimized = ref(false);
const tabBarHeight = ref(50);

// 面板位置常量  
const PANEL_POSITIONS = {
  EXPANDED: 0,     // 完整显示按钮和广告（初始状态）
  MINIMIZED: 0,    // 只显示按钮（下拉状态）
};

// 响应式高度值
const screenHeight = ref(0);
const panelHeight = ref(0);

// 初始化面板位置
const initializePanelPositions = () => {
  const systemInfo = uni.getSystemInfoSync();
  screenHeight.value = systemInfo.windowHeight; // 获取实际屏幕高度
  
  // 计算面板高度，调整为更矮的比例（约33%的屏幕高度）
  panelHeight.value = Math.round(screenHeight.value * 0.38);
  
  const advertHeight = 160; // 轮播图高度（rpx转px约65px）
  
  // 立即设置面板位置常量，不等异步回调
  PANEL_POSITIONS.EXPANDED = 0;
  PANEL_POSITIONS.MINIMIZED = advertHeight;
  
  // 设置初始位置为完整显示状态
  panelPosition.value = PANEL_POSITIONS.EXPANDED;
  isMinimized.value = false;
  
  // 获取自定义底边栏的实际高度（异步操作，但不影响拖拽）
  const query = uni.createSelectorQuery();
  query.select('.custom-tab-bar').boundingClientRect(data => {
    if (data) {
      tabBarHeight.value = data.height;
    }
  }).exec();
};

// 拖拽处理函数
const handleTouchStart = (e) => {
  if (!e.touches || !e.touches[0]) {
    return;
  }
  
  console.log('拖拽开始', {
    EXPANDED: PANEL_POSITIONS.EXPANDED,
    MINIMIZED: PANEL_POSITIONS.MINIMIZED,
    currentPosition: panelPosition.value
  });
  
  isDragging.value = true;
  startY.value = e.touches[0].clientY;
  startPosition.value = panelPosition.value;
  
  // 阻止事件冒泡和默认行为
  e.stopPropagation();
};

const handleTouchMove = (e) => {
  if (!isDragging.value) {
    return;
  }

  // 阻止事件冒泡和默认行为
  e.preventDefault();
  e.stopPropagation();

  // 确保触摸事件存在
  if (!e.touches || !e.touches[0]) {
    return;
  }

  const currentY = e.touches[0].clientY;
  const deltaY = currentY - startY.value;
  const newPosition = startPosition.value + deltaY;

  // 限制拖动范围：EXPANDED(0) 到 MINIMIZED(130)
  panelPosition.value = Math.max(
    PANEL_POSITIONS.EXPANDED,  // 最小值 0
    Math.min(PANEL_POSITIONS.MINIMIZED, newPosition) // 最大值 130
  );
};

const handleTouchEnd = (e) => {
  // 阻止事件冒泡和默认行为
  e.preventDefault();
  e.stopPropagation();
  
  isDragging.value = false;

  // 计算当前位置到两个固定位置的距离
  const distanceToExpanded = Math.abs(panelPosition.value - PANEL_POSITIONS.EXPANDED);
  const distanceToMinimized = Math.abs(panelPosition.value - PANEL_POSITIONS.MINIMIZED);
  const totalDistance = Math.abs(PANEL_POSITIONS.MINIMIZED - PANEL_POSITIONS.EXPANDED);

  // 如果移动距离超过总距离的 1/3，则吸附到对应位置
  if (distanceToExpanded > totalDistance / 3 && distanceToMinimized > totalDistance / 3) {
    // 根据移动方向决定吸附位置
    const isMovingDown = panelPosition.value > startPosition.value;
    panelPosition.value = isMovingDown ? PANEL_POSITIONS.MINIMIZED : PANEL_POSITIONS.EXPANDED;
    isMinimized.value = isMovingDown;
  } else {
    // 吸附到最近的位置
    const shouldMinimize = distanceToMinimized < distanceToExpanded;
    panelPosition.value = shouldMinimize ? PANEL_POSITIONS.MINIMIZED : PANEL_POSITIONS.EXPANDED;
    isMinimized.value = shouldMinimize;
  }
};

// 按钮点击事件
const handleParkingPayment = () => {
  if (uni.getStorageSync("token")) {
    uni.navigateTo({
      url: "/pages/payPlateQuery/payPlateQuery",
    });
  } else {
    uni.showModal({
      title: "提示",
      content: "请先登录",
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({
            url: "/pages/login/login",
          });
        }
      },
    });
  }
};

const handleChargingCode = () => {
  uni.showToast({
    title: '暂未开放',
    icon: 'none',
    duration: 2000
  });
};
</script>

<style lang="scss" scoped>
.map-container {
  height: 100vh;
}

// 页面内容
.page-content {
  margin-top: 88rpx;
  padding: 32rpx;
  padding-bottom: 140rpx;
}

// 浮动遮罩层
.floating-overlay {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  will-change: transform;
  transform: translateZ(0);
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -webkit-overflow-scrolling: touch;

  &.overlay-dragging {
    transition: none !important;
  }
}

// 拖拽手柄容器
.drag-handle-container {
  height: 50rpx; // 适中的触摸区域高度
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }

  .drag-handle-bar {
    width: 60rpx;
    height: 6rpx;
    background: #cfcfcf;
    border-radius: 3rpx;
    pointer-events: none;
  }
}

// 按钮容器
.buttons-container {
  padding: 5rpx 30rpx 25rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 24rpx;
  position: relative;
  z-index: 1;

  .action-button {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20rpx 16rpx;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 6rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid #f0f0f0;


    .button-text {
      margin-top: 12rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #000000;
    }
  }
}

// 主要内容区域
.overlay-content {
  padding: 0 30rpx;
  position: relative;
  z-index: 0;

  // 轮播图区域样式
  .banner-section {
    border-radius: 24rpx;
    transition: opacity 0.3s ease;
    
    &.content-hidden {
      opacity: 0;
      pointer-events: none;
    }
  }
}

// 场库类型切换按钮
.map-controls {
  position: absolute;
  left: 20rpx;
  display: flex;
  flex-direction: column; // 竖向排列
  gap: 15rpx;
  z-index: 10;

  .control-button {
    padding: 12rpx 18rpx;
    border-radius: 30rpx;
    background-color: #ffffff;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &.active {
      background: linear-gradient(135deg, #4BA1FC, #777efe);
      color: #ffffff;

      .button-text {
        color: #ffffff;
      }
    }

    &:active {
      transform: scale(0.95);
    }

    .button-text {
      font-size: 26rpx;
      font-weight: 500;
      color: #333;
      text-align: center;
      white-space: nowrap;
    }
  }
}
</style>
