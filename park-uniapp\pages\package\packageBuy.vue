<template>
    <view class="package-buy">
        <view class="notice-card">
            <view class="notice-content">
                <text class="notice-text">
                    {{ noticeText }}
                </text>
            </view>
        </view>

        <!-- 订单信息卡片 -->
        <view class="cell">
            <view class="content">
                <view class="content_item">
                    <view class="title">
                        <image src="/static/package/changku.png" mode="aspectFit"></image>场库名称
                    </view>
                    <view class="word red"> {{ packageOrder.warehouseName || '-' }}</view>
                </view>
                <view class="content_item">
                    <view class="title">
                        <image src="/static/package/chepai.png" mode="aspectFit"></image>车牌牌号
                    </view>
                    <view class="word"> {{ packageOrder.plateNo || '-' }} </view>
                </view>
                <view class="content_item">
                    <view class="title">
                        <image src="/static/package/taocantype.png" mode="aspectFit"></image>套餐类型
                    </view>
                    <view class="word"> {{ packageOrder.packageName || '-' }} </view>
                </view>
                <!-- 如果是续费，显示当前到期时间 -->
                <view class="content_item">
                    <view class="title">
                        <image src="/static/package/kaitong.png" mode="aspectFit"></image>开始时间
                    </view>
                    <view class="word" :class="[canSelectTime ? 'clickable-text' : '']">
                        <!-- 日期选择器 -->
                        <picker v-if="canSelectTime" mode="date" :value="selectedDate" :start="minDate" :end="maxDate"
                            @change="onDateChange">
                            <view class="picker-display">
                                {{ packageOrder.beginVipTime || '请选择开始时间' }}
                            </view>
                        </picker>
                        <view v-else class="picker-display">
                            {{ packageOrder.beginVipTime || '请选择开始时间' }}
                        </view>
                    </view>
                </view>
                <view v-if="packageOrder.isRenewal" class="content_item">
                    <view class="title">
                        <image src="/static/package/kaitong.png" mode="aspectFit"></image>当前到期时间
                    </view>
                    <view class="word"> {{ packageOrder.expirationTime }} </view>
                </view>
                <view class="content_item">
                    <view class="title">
                        <image src="/static/package/kaitong.png" mode="aspectFit"></image>{{ packageOrder.isRenewal ?
                        '续费后到期时间' : '到期时间' }}
                    </view>
                    <view class="word"> {{ (packageOrder.newExpirationTime || packageOrder.expirationTime) || '--' }} </view>
                </view>
                <view class="content_item">
                    <view class="title">
                        <image src="/static/package/taocanprice.png" mode="aspectFit"></image>套餐价格
                    </view>
                    <view class="word">
                        <text class="tips"> ¥ </text> {{ packageOrder.packagePrice || '-' }}
                    </view>
                </view>
                <view class="content_item">
                    <view class="title">
                        <image src="/static/package/shifu.png" mode="aspectFit"></image>实付金额
                    </view>
                    <view class="word money">
                        <text class="tips red"> ¥ </text> {{ packageOrder.packagePrice || '0.00' }}
                    </view>
                </view>
            </view>
            <button @tap="submitOrder" class="btn">{{ packageOrder.isRenewal ? '确认续费' : '提交订单' }}</button>
        </view>
    </view>
</template>

<script setup>
import { ref, computed} from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { createOrder, updateOrder} from '@/api/package';
import { checkCarInWarehouse } from '@/api/package';
import { getOpenid } from '@/api/login';
import { isSameDate, extractDatePart, formatDate, createDate } from '@/utils/utils';

const packageOrder = ref({});
// 是否已是会员
const isExistingMember = ref(false);
// 是否在场
const isCarInWarehouse = ref(false);
const packageJudge = ref({});
// 是否可选择时间
const canSelectTime = ref(false);   
const selectedDate = ref('');

// 日期范围设置
const minDate = ref('');
const maxDate = ref(''); 

// 计算购买前提示文本
const noticeText = computed(() => {
    if (packageOrder.value.isRenewal) {
        return `当前选择的续费套餐是：${packageOrder.value.packageName || ''}(${packageOrder.value.packageDays || ''}个自然日)。
        续费将在原有套餐到期日期基础上延长，无法修改时间`;
    }
    
    if (isCarInWarehouse.value) {
        // 检查是否有会员过期或临停缴费记录
        const hasExpiredMember = packageJudge.value.endVipTime !== null;
        const hasParkingPayment = packageJudge.value.endParkingTime !== null;
        
        if (hasExpiredMember || hasParkingPayment) {
            return `当前选择的套餐是：${packageOrder.value.packageName || ''}(${packageOrder.value.packageDays || ''}个自然日)。
            系统检测您在场期间有会员过期或者临停缴费记录，系统已为您分配对应的时间，如有问题，请联系客服。`;
        } else {
            return `当前选择的套餐是：${packageOrder.value.packageName || ''}(${packageOrder.value.packageDays || ''}个自然日)。
            系统检测到您的车辆在场，将采用您的入场日期（${packageJudge.value.finalBeginTime}）作为会员开始日期，额外0-1天优惠。`;
        }
    }
    
    return `当前选择的开通套餐是：${packageOrder.value.packageName || ''}(${packageOrder.value.packageDays || ''}个自然日)。
    您可以选择开始日期，选择当天开始享受额外0-1天优惠。`;
});

onLoad((options) => {
    packageOrder.value = JSON.parse(options.packageOrder);
    // 是否已是会员，是的话续费订单，否则是首次开通订单
    isExistingMember.value = packageOrder.value.isRenewal;
    console.log('isExistingMember.value: ', isExistingMember.value)
    console.log('packageOrder.value: ', packageOrder.value) 

    // 判断是否是续费订单
    if (isExistingMember.value) {
        handleRenewalOrder();
    } else {
        // 首次开通，需要检查车辆是否在场
        checkCarInWarehouse({
            plateNo: packageOrder.value.plateNo,
            warehouseId: packageOrder.value.warehouseId
        }).then(res => {
            packageJudge.value = res.data;
            isCarInWarehouse.value = res.data && res.data.isCarInWarehouse;
            handleFirstTimeOrder();
        }).catch(err => {
            console.log('检查车辆是否在场失败：', err);
            isCarInWarehouse.value = false;
            handleFirstTimeOrder();
        });
    }
});

// 处理续费订单
const handleRenewalOrder = () => {
    // 验证续费订单必须有原开始时间和结束时间
    if (!packageOrder.value.beginVipTime || !packageOrder.value.expirationTime) {
        uni.showToast({
            title: '续费订单数据异常，请重新操作',
            icon: 'none',
            duration: 2000
        });
        return;
    }
    
    // 续费订单：开始时间保持原开始时间，结束时间在原结束时间基础上延长
    const originalEndDate = new Date(packageOrder.value.expirationTime);
    const newEndDate = new Date(originalEndDate);
    newEndDate.setDate(originalEndDate.getDate() + packageOrder.value.packageDays);
    
            const endDateStr = formatDate(newEndDate);
        packageOrder.value.newExpirationTime = `${endDateStr} 23:59:59`;
    
    // 续费订单不允许选择时间
    packageOrder.value.canSelectTime = false;
};

// 处理会员开通
const handleFirstTimeOrder = () => {
    if (isCarInWarehouse.value) {
        handleCarInWarehouse();
    } else {
        handleCarNotInWarehouse();
    }
};

// 处理车辆在场的开通会员
const handleCarInWarehouse = () => {
    // 检查是否有会员过期记录
    const hasExpiredMember = packageJudge.value.endVipTime !== null;
    
    if (hasExpiredMember && packageJudge.value.finalBeginTime) {
        // 如果有会员过期记录，需要判断finalBeginTime是来自会员过期还是临停缴费
        const finalBeginDate = new Date(packageJudge.value.finalBeginTime);
        let startDate = new Date(finalBeginDate);
        
        // 判断finalBeginTime是否来自临停缴费（临停缴费时间更晚）
        const hasParkingPayment = packageJudge.value.endParkingTime !== null;
        const isFinalBeginFromParking = hasParkingPayment && 
            packageJudge.value.finalBeginTime === packageJudge.value.endParkingTime;
        
        if (isFinalBeginFromParking) {
            // 如果finalBeginTime来自临停缴费时间，使用finalBeginTime的0点（不加一天）
            startDate.setHours(0, 0, 0, 0);
        } else {
            // 如果finalBeginTime来自会员过期时间，使用finalBeginTime+1天的0点
            startDate.setDate(finalBeginDate.getDate() + 1);
            startDate.setHours(0, 0, 0, 0);
        }
        
        const startDateStr = formatDate(startDate);
        packageOrder.value.beginVipTime = `${startDateStr} 00:00:00`;
        
        // 计算结束时间：需要判断开始时间是否是今天
        const today = new Date();
        const endDate = new Date(startDate);
        const isToday = isSameDate(startDate, today);
        
        if (isToday) {
            // 如果是今天，送用户0-1天优惠
            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);
        } else {
            // 如果不是今天，正常计算（不送优惠天数）
            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1);
        }
        
        const endDateStr = formatDate(endDate);
        packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;
    } else {
        // 没有会员过期记录，使用接口返回的finalBeginTime作为开始时间
        if (packageJudge.value.finalBeginTime) {
            // 将finalBeginTime转换为日期部分并设置为00:00:00
            const datePart = extractDatePart(packageJudge.value.finalBeginTime);
            packageOrder.value.beginVipTime = `${datePart} 00:00:00`;
            
            // 计算结束时间：开始时间+套餐天数（车辆在场额外优惠）
            const startDate = new Date(packageOrder.value.beginVipTime);
            const endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);
            
            const endDateStr = formatDate(endDate);
            packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;
        } else {
            uni.showToast({
                title: '参数错误，请联系客服',
                icon: 'none',
                duration: 2000
            });
            return;
        }
    }
    
    packageOrder.value.canSelectTime = false;
};

// 处理车辆不在场开通会员
const handleCarNotInWarehouse = () => {
    // 车辆不在场，允许选择开始时间
    canSelectTime.value = true;
    
    // 设置默认开始时间为今天（可享受额外优惠）
    const today = new Date();
    const dateStr = formatDate(today);
    // 会员开始时间
    packageOrder.value.beginVipTime = `${dateStr} 00:00:00`;
    
    // 设置(开始时间）选择范围
    updateDateRange();
    
    // 计算结束时间
    calculateEndDate();
    
};

// 设置日期选择范围
const updateDateRange = () => {
    // 如果不允许选择时间，不需要设置日期范围
    if (!canSelectTime.value) {
        return;
    }
    
    // 最小日期为今天
    const today = new Date();
    minDate.value = formatDate(today);
    
    // 最大日期为3个月后 - 使用明确的日期计算
    const maxDateObj = new Date();
    maxDateObj.setMonth(today.getMonth() + 3);
    maxDate.value = formatDate(maxDateObj);
    
    // 设置默认选中日期
    selectedDate.value = formatDate(today);
    
};

// 日期选择改变
const onDateChange = (e) => {
    if (!canSelectTime.value) {
        return;
    }
    
    // 原生picker的事件格式
    const selectedDateStr = e.detail.value;
    
    // 判断选择的日期是否是今天
    const selectedDateObj = createDate(selectedDateStr);
    const today = new Date();
    const isToday = isSameDate(selectedDateObj, today);
    
    // 为选择的日期添加00:00:00时间
    packageOrder.value.beginVipTime = `${selectedDateStr} 00:00:00`;
    
    // 更新selectedDate的值
    selectedDate.value = selectedDateStr;
    
    // 计算到期时间
    calculateEndDate();
};

// 计算到期时间（仅用于首次开通）
const calculateEndDate = () => {
    if (packageOrder.value.beginVipTime && packageOrder.value.packageDays) {
        const startDate = new Date(packageOrder.value.beginVipTime);
        const today = new Date();
        const endDate = new Date(startDate);
        
        // 判断开始时间是否是今天（使用更可靠的日期比较方法）
        const isToday = isSameDate(startDate, today);
        
        if (isToday) {
            // 如果是今天，送用户0-1天，从第二天开始算天数
            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays);
        } else {
            // 如果不是今天，正常计算（开始日期+套餐天数-1天）
            endDate.setDate(startDate.getDate() + packageOrder.value.packageDays - 1);
        }

        const endDateStr = formatDate(endDate);
        packageOrder.value.expirationTime = `${endDateStr} 23:59:59`;
    }
};



// 提交订单
const submitOrder = () => {
    // 校验到期时间不能早于当前时间
    const currentTime = new Date();
    let endTime;
    
    // 根据是否为续费选择相应的到期时间
    if (packageOrder.value.isRenewal && packageOrder.value.newExpirationTime) {
        endTime = new Date(packageOrder.value.newExpirationTime);
    } else if (packageOrder.value.expirationTime) {
        endTime = new Date(packageOrder.value.expirationTime);
    } else {
        uni.showToast({
            title: '套餐到期时间异常，请重新选择',
            icon: 'none',
            duration: 2000
        });
        return;
    }
    
    // 检查到期时间是否早于当前时间
    if (endTime <= currentTime) {
        uni.showToast({
            title: '套餐到期时间不能早于当前时间，请重新选择',
            icon: 'none',
            duration: 3000
        });
        return;
    }
    uni.login({
        success: async (loginRes) => {
            try {
                const openidRes = await getOpenid({
                    wxCode: loginRes.code
                })
                
                // 将openid存储到packageOrder中
                packageOrder.value.openid = openidRes.data
                
                // 继续创建订单
                createOrderWithOpenid()
            } catch (error) {
                console.error('获取openid失败:', error)
                uni.showToast({
                    title: '获取用户信息失败',
                    icon: 'none',
                    duration: 2000
                })
            }
        },
        fail: (error) => {
            console.error('登录失败:', error)
            uni.showToast({
                title: '登录失败，请重试',
                icon: 'none',
                duration: 2000
            })
        }
    })
}

// 创建订单（在获取openid后调用）
const createOrderWithOpenid = () => {
    uni.showLoading({
        title: '加载中...',
        mask: true
    })
    
    // console.log('packageOrder.value: ', packageOrder.value)
    createOrder(packageOrder.value).then(res => {
        console.log('创建订单 res: ', res)
        if (res.data.needPay) {
            uni.requestPayment({
                timeStamp: res.data.timeStamp,
                nonceStr: res.data.nonceStr,
                package: res.data.package,
                signType: res.data.signType,
                paySign: res.data.paySign,
                success: function (result) {
                    // 延迟一下显示toast，避免与complete中的hideLoading冲突
                    uni.hideLoading()
                    setTimeout(() => {
                        uni.showToast({
                            title: '支付成功~',
                            icon: 'none',
                            duration: 2000
                        })
                    }, 100)
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 2000)
                },
                fail: function (err) {
                    uni.hideLoading()
                    console.log('支付失败的回调：', err)

                    // 调用更新订单接口，将状态改为已取消
                    if (res.data.orderId) {
                        updateOrder({
                            id: res.data.orderId,
                            payStatus: 3  // 已取消
                        }).then(updateRes => {
                            console.log('订单状态更新为已取消：', updateRes)
                        }).catch(updateErr => {
                            console.log('订单状态更新失败：', updateErr)
                            // 即使更新失败也不影响用户体验
                        })
                    }

                    // 延迟一下显示toast，避免与hideLoading冲突
                    setTimeout(() => {
                        uni.showToast({
                            title: '支付失败',
                            icon: 'none',
                            duration: 1500
                        })
                    }, 100)
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 2000)
                },
                complete: function (res) {
                    uni.hideLoading()
                }
            })
        } else {
            uni.hideLoading()
            setTimeout(() => {
                uni.showToast({
                    title: '开通成功~',
                    icon: 'none',
                    duration: 2000
                })
            }, 100)
            setTimeout(() => {
                uni.navigateBack()
            }, 2000)
        }
    }).catch(err => {
        console.log(err);
        uni.hideLoading()
        setTimeout(() => {
            uni.showToast({
                title: '订单创建失败',
                icon: 'none',
                duration: 1500
            })
        }, 100)
        setTimeout(() => {
            uni.navigateBack()
        }, 2000)
    })
}
</script>

<style lang="scss" scoped>
.package-buy {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding: 20rpx;
}
.notice-card {
    background: linear-gradient(135deg, #fff3e0 0%, #ffeacb 100%);
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    
    .notice-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12rpx;
        
        .notice-text {
            font-size: 26rpx;
            color: #e65100;
            line-height: 1.5;
            flex: 1;
        }
    }
}

.cell {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.content {
    margin-bottom: 40rpx;
}

.content_item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    transition: all 0.2s ease;
}

.title {
    display: flex;
    align-items: center;
    font-size: 28rpx;
    color: #333;
    
    image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 15rpx;
    }
}

.word {
    font-size: 28rpx;
    color: #666;
    
    &.red {
        color: #ff4757;
        font-weight: bold;
    }
    
    &.money {
        font-size: 32rpx;
        font-weight: bold;
        color: #ff4757;
    }
    
    &.clickable-text {
        color: #4BA1FC;
        font-weight: 500;
        text-decoration: underline;
        text-decoration-color: rgba(75, 161, 252, 0.3);
        text-underline-offset: 4rpx;
    }
    
    .picker-display {
        width: 100%;
        text-align: right;
        color: inherit;
    }
    
    &.dikou {
        display: flex;
        align-items: center;
        color: #ff6b35;
        
        .desc {
            font-size: 24rpx;
            margin-right: 10rpx;
        }
        
        image {
            width: 24rpx;
            height: 24rpx;
            margin-left: 10rpx;
        }
    }
    
    .disabled-tip {
        font-size: 22rpx;
        color: #999;
        margin-left: 10rpx;
    }
}

.tips {
    font-size: 24rpx;
    
    &.red {
        color: #ff4757;
    }
    
    &.orange {
        color: #ff6b35;
    }
}

.btn {
    width: 100%;
    background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);;
    color: #fff;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    border: none;
    box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
    
    &:active {
        transform: translateY(1rpx);
        box-shadow: 0 4rpx 10rpx rgba(102, 126, 234, 0.3);
    }
}

// 弹窗样式
.popup-content {
    background-color: #fff;
    border-radius: 20rpx;
    padding: 60rpx 40rpx 40rpx;
    width: 600rpx;
    text-align: center;
}

.popup-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
}

.popup-desc {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 50rpx;
}

.popup-buttons {
    display: flex;
    gap: 30rpx;
}

.popup-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    border: none;
    
    &.cancel {
        background-color: #f5f5f5;
        color: #666;
    }
    
    &.confirm {
        background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
        color: #fff;
    }
}
</style>