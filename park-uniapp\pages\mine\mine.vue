<template>
    <view class="mine-container">
        <!-- 头部卡片 -->
        <view class="user-info-section">
            <!-- 用户信息区域 -->
            <view class="user-info" @tap="routeEdit">
                <image class="avatar" :src="userInfo && userInfo.img ? userInfo.img : '/static/mine/avatar.png'"
                    mode="aspectFill">
                </image>
                <view class="user-detail">
                    <view class="nickname">{{ userInfo && userInfo.nickName ? userInfo.nickName : '未登录' }}</view>
                    <view class="user-tags" v-if="specialUser && specialUser.userType">
                        <view class="tag" :class="getTagClass(specialUser.userType)">
                            {{ judgeUserType(specialUser.userType) }}
                        </view>
                    </view>
                </view>
                <u-icon name="arrow-right" size="24" color="#666666" class="arrow-right"></u-icon>
            </view>
            <!-- 充电账户信息 -->
            <view class="account-card">
                <view class="card-content">
                    <view class="left">
                        <view class="title">累计充电总金额</view>
                        <view class="amount">
                            <text class="symbol">￥</text>
                            <text class="number">0.00</text>
                        </view>
                    </view>
                    <view class="right">
                        <view class="view-btn" @click="showDevelopingTip">查看</view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 功能列表 -->
        <view class="function-list">
            <!-- 车辆管理 -->
            <view class="section-title">车辆管理</view>
            <view class="car-card" @click="routePage('/pages/myCar/myCar')">
                <image src="/static/image/carLeft.png" mode="widthFix" class="car-image"></image>
                <view class="car-info">
                    <view class="label">我的车辆</view>
                    <view class="plate-number">{{ defaultCar == null ? '--' : defaultCar }}</view>
                </view>
                <view class="manage-btn">去管理</view>
            </view>

            <!-- 其他功能 -->
            <view class="section-title">其他功能</view>
            <view class="grid-list">
                <view class="grid-item" v-for="(item, index) in functionList" :key="index"
                    @click="handleFunctionClick(item)">
                    <image :src="item.icon" mode="aspectFit" class="icon"></image>
                    <text class="name">{{ item.name }}</text>
                </view>
            </view>
        </view>

        <custom-tab-bar></custom-tab-bar>
    </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { getCarList } from '@/api/car';
import { getSpecialUser } from '@/api/specialUser';
import CustomTabBar from "@/components/custom-tab-bar/index.vue";

const userInfo = ref(null);
const isLogin = ref(false);
const defaultCar = ref(null);
const specialUser = ref({});

// 初始化用户数据
const initUserData = () => {
    userInfo.value = uni.getStorageSync('wxUser');
    console.log(userInfo.value);
    if (uni.getStorageSync('token')) {
        isLogin.value = true;
    } else {
        isLogin.value = false;
    }
    if (isLogin.value) {
        // 获取车辆列表
        getCarList().then(res => {
            console.log(res);
            defaultCar.value = res.data?.length > 0 ? res.data[0].plateNo : '--';
        });
        
        // 获取用户类型信息
        getSpecialUser().then(res => {
            specialUser.value = res.data || {};
            console.log('specialUser:', specialUser.value);
        });
    } else {
        // 未登录时重置数据
        defaultCar.value = '--';
        specialUser.value = {};
    }
};

onShow(() => {
    initUserData();
});

const judgeUserType = (userType) => {
    if (userType === 'VIP客户') {
        return 'VIP客户';
    } else if (userType === '集团客户') {
        return '集团客户';
    } else {
        return '普通用户';
    }
};

// 根据用户类型返回对应的标签样式类名
const getTagClass = (userType) => {
    if (userType === 'VIP客户') {
        return 'tag-vip';        // VIP客户
    } else if (userType === '集团客户') {
        return 'tag-enterprise';  // 集团客户
    } else {
        return 'tag-normal';      // 普通用户
    }
};

// 功能列表
const functionList = ref([
    // {
    //     name: '充电订单',
    //     icon: '/static/mine/chargeOrder.png',
    //     path: '/pages/chargeOrder'
    // },
     {
         name: '抬头管理',
        icon: '/static/mine/invoiceTitle​.png',
         path: '/pages/invoice/invoiceTitle'
     },
     {
         name: '停车发票',
         icon: '/static/mine/invoiceManage.png',
         path: '/pages/invoice/invoiceManage'
     },
    // {
    //     name: '充电开票',
    //     icon: '/static/mine/chargeInvoice.png',
    //     path: '/pages/chargeInvoice'
    // },
    // {
    //     name: '帮人购买',
    //     icon: '/static/mine/vipGift.png',
    //     path: '/pages/vipGift'
    // },
    // {
    //     name: '我的卡包',
    //     icon: '/static/mine/​​couponMine.png',
    //     path: '/pages/couponMine'
    // },
    // {
    //     name: '用户设置',
    //     icon: '/static/mine/userSet.png',
    //     path: '/pages/userSet'
    // },
    {
        name: '临停订单',
        icon: '/static/mine/chargeOrder.png',
        path: '/pages/parkingOrder/parkingOrder'
    },
    {
        name: '停车套餐订单',
        icon: '/static/mine/chargeOrder.png',
        path: '/pages/package/packageRecord'
    },
    {
        name: '退出登录',
        icon: '/static/mine/logout.png',
        action: 'logout'
    }
]);

// 页面跳转
const routePage = (url) => {
    uni.navigateTo({
        url: url
    });
};

// 显示开发中提示
const showDevelopingTip = () => {
    uni.showToast({
        title: '开发中...',
        icon: 'none',
        duration: 2000
    });
};

// 跳转到登录/编辑页面
const routeEdit = () => {
    uni.navigateTo({
        url: '/pages/mine/mineEdit'
    });
};

// 处理功能点击
const handleFunctionClick = (item) => {
    if (item.action === 'logout') {
        if (isLogin.value) {
            // 已登录用户的退出逻辑
            uni.showModal({
                content: '确定要退出登录吗？',
                cancelText: '取消',
                confirmText: '确认',
                success: res => {
                    if (res.confirm) {
                        uni.removeStorageSync("token");
                        uni.removeStorageSync("wxUser");
                        uni.removeStorageSync("currentWarehouse");
                        uni.showToast({
                            title: '退出登录成功',
                            duration: 2000
                        });
                        // 重新初始化用户数据
                        initUserData();
                    }
                }
            });
        } else {
            // 未登录用户的提示逻辑
            uni.showModal({
                title: '温馨提示',
                content: '您还未登录，是否先去登录？',
                cancelText: '取消',
                confirmText: '确定',
                success: res => {
                    if (res.confirm) {
                        uni.navigateTo({
                            url: '/pages/login/login'
                        });
                    }
                }
            });
        }
    } else if (item.path) {
        routePage(item.path);
    }
};
</script>

<style lang="scss" scoped>
.mine-container {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding: 24rpx 24rpx;
}

.user-info-section {
    border-radius: 24rpx;
    background-color: #fff;
    padding: 28rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);

    .user-info {
        display: flex;
        align-items: center;
        padding: 20rpx 0;

        .avatar {
            width: 120rpx;
            height: 120rpx;
            border-radius: 50%;
            border: 4rpx solid #fff;
            margin-right: 24rpx;
        }

        .user-detail {
            .nickname {
                font-size: 36rpx;
                color: #000000;
                margin-bottom: 12rpx;
            }

            .user-tags {
                padding-top: 10rpx;
                display: flex;

                .tag {
                    padding: 6rpx 16rpx;
                    border-radius: 24rpx;
                    font-size: 24rpx;
                    
                    // 普通用户 - 蓝色
                    &.tag-normal {
                        background-color: #E3F2FD;
                        color: #1976D2;
                    }
                    
                    // 集团客户 - 橙色
                    &.tag-enterprise {
                        background-color: #FFF3E0;
                        color: #F57C00;
                    }
                    
                    // VIP客户 - 金色
                    &.tag-vip {
                        background-color: #FFF8E1;
                        color: #F9A825;
                    }
                    
                    // 未知类型 - 灰色
                    &.tag-unknown {
                        background-color: #F5F5F5;
                        color: #757575;
                    }
                }
            }
        }

        .arrow-right {
            margin-left: auto;
        }
    }
}

.account-card {
    background: linear-gradient(90deg, #4BA1FC 0%, #9b8eff 100%);
    border-radius: 24rpx;
    padding: 24rpx;
    color: #fff;
    position: relative;
    z-index: 1;

    .card-content {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .left {
            .title {
                font-size: 28rpx;
                margin-bottom: 16rpx;
            }

            .amount {
                .symbol {
                    font-size: 32rpx;
                }

                .number {
                    font-size: 48rpx;
                    font-weight: 500;
                }
            }
        }

        .right {
            .view-btn {
                background: rgba(255, 255, 255, 0.2);
                padding: 12rpx 32rpx;
                border-radius: 32rpx;
                font-size: 28rpx;
            }
        }
    }
}

.function-list {
    background: #fff;
    border-radius: 24rpx;
    padding: 32rpx;

    .section-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 24rpx;
    }

    .car-card {
        background: #eaf3ff;
        border-radius: 16rpx;
        padding: 24rpx;
        display: flex;
        align-items: center;
        margin-bottom: 32rpx;

        .car-image {
            width: 140rpx;
        }

        .car-info {
            margin-left: 50rpx;
            .label {
                font-size: 28rpx;
                color: #666666;
                margin-bottom: 12rpx;
            }

            .plate-number {
                font-size: 32rpx;
                font-weight: bold;
                color: #333;
            }
        }

        .manage-btn {
            background: linear-gradient(90deg, #4BA1FC 0%, hsl(240, 100%, 78%) 100%);
            color: #fff;
            padding: 12rpx 32rpx;
            border-radius: 32rpx;
            font-size: 28rpx;
            margin-left: auto;
        }
    }

    .grid-list {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 32rpx;

        .grid-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12rpx;
            padding: 16rpx 0;
            border-radius: 12rpx;
            transition: all 0.2s ease;

            &:active {
                background-color: #f5f5f5;
            }
            
            .icon {
                width: 64rpx;
                height: 64rpx;
                transition: transform 0.2s ease;
            }

            .name {
                font-size: 24rpx;
                color: #333;
                transition: color 0.2s ease;
            }
        }
    }
}
</style>
