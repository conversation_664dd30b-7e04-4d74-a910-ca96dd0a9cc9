<template>
    <div class="home">
        <div class="home_bg">
            <div class="cell">
                <div class="scan_cell">
                    <div class="cell_header">
                        <div class="scan_cell_title">临停缴费</div>
                        <div class="scan_cell_img"></div>
                    </div>
                    <div class="cell_input">
                        <div class="cell_title">{{ carInfo?.plateNo || '--' }}</div>
                        <div class="cell_item">
                            <div class="">停车地点</div>
                            <div class="">{{ carInfo?.warehouseName || '--' }}</div>
                        </div>
                        <div class="cell_item">
                            <div class="">入场时间</div>
                            <div class="">{{ carInfo?.beginParkingTime || '--' }}</div>
                        </div>
                        <div class="cell_item">
                            <div class="">停车时长</div>
                            <div class="">{{ formatParkingDuration(carInfo?.parkingDuration) || '--' }}</div>
                        </div>
                        <div class="cell_item">
                            <div class="">应缴金额</div>
                            <div class="">{{ carInfo?.paymentAmount || '--' }}</div>
                        </div>
                        <div class="cell_input_title">选择支付方式</div>
                        <div class="cell_input_radio">
                            <el-radio-group v-model="chooseRadio" @change="handleChange">
                                <el-radio label="1" border>微信</el-radio>
                                <el-radio label="2" border>支付宝</el-radio>
                            </el-radio-group>
                        </div>
                    </div>
                    <div class="submit-btn" @click="handleClick">
                        {{ getButtonText() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRoute } from 'vue-router'
import { channelPayQuery, paymentTemporaryAlipay } from '@/api/webPage'
const route = useRoute()
const { proxy } = getCurrentInstance()
// 订单信息
const carInfo = ref(null)
// 支付方式
const chooseRadio = ref('1')
// 按钮是否禁用
const isDisabled = ref(false)
// 跳转状态提示
const jumpStatus = ref('')
// 支付方式切换
function handleChange(e) {
    chooseRadio.value = e
}

// 出场扫码到当前页面，根据gateNo和warehouseId查询订单信息
onMounted(() => {
    if (route.query.gateNo && route.query.warehouseId) {
        let params = {
            gateNo: route.query.gateNo,
            warehouseId: route.query.warehouseId
        }
        console.log(params)
        channelPayQuery(params).then(res => {
            carInfo.value = res.data || {}
        })
    }
})

// 获取按钮文本
function getButtonText() {
    if (!isDisabled.value) {
        return '确认支付'
    }

    if (jumpStatus.value) {
        return jumpStatus.value
    }

    return chooseRadio.value === '1' ? '正在跳转微信...' : '正在跳转支付宝...'
}
// 确认支付
function handleClick() {
    if (!carInfo.value) {
        proxy.$message.error('当前订单不存在~')
        return
    }
    if (!carInfo.value?.paymentAmount) {
        proxy.$message.error('当前应缴金额为0~')
        return
    }
    if (isDisabled.value) {
        return
    }
    isDisabled.value = true
    jumpStatus.value = ''

    if (chooseRadio.value === '1') {
        // 微信支付 - 跳转到小程序
        jumpStatus.value = '正在跳转微信...'
        jumpToMiniProgram()
    } else {
        // 支付宝支付
        jumpStatus.value = '正在跳转支付宝...'
        handleAlipayPayment()
    }
}

// 跳转到微信小程序
function jumpToMiniProgram() {
    try {
        // 体验版暂不支持微信支付提示 - 正式版时注释此段
        proxy.$message.warning('体验版暂不支持微信支付，请使用支付宝支付')
        return
        // 体验版提示结束

        // 验证必要参数
        if (!route.query.gateNo || !route.query.warehouseId) {
            proxy.$message.error('缺少必要的支付参数')
            isDisabled.value = false
            jumpStatus.value = ''
            return
        }

        jumpStatus.value = '正在跳转微信小程序...'

        // 使用明文scheme直接跳转正式版
        const stopNoValue = `${route.query.gateNo}wId${route.query.warehouseId}`
        const weixinUrl = `weixin://dl/business/?appid=wxdcd31ee3e79190cc&path=pages/carStop/carStop&query=stopNo%3D${encodeURIComponent(stopNoValue)}&env_version=release`

        console.log('跳转到正式版小程序:', weixinUrl)

        // 直接跳转到正式版小程序
        window.open(weixinUrl, '_blank')

        // 重置状态
        setTimeout(() => {
            isDisabled.value = false
            jumpStatus.value = ''
        }, 2000)

    } catch (error) {
        console.error('跳转微信小程序失败:', error)
        proxy.$message.error('跳转微信小程序失败，请确保已安装微信')
        isDisabled.value = false
        jumpStatus.value = ''
    }
}
// 处理支付宝支付
function handleAlipayPayment() {
    const params = {
        gateNo: route.query.gateNo,
        warehouseId: route.query.warehouseId
    }

    paymentTemporaryAlipay(params).then(res => {
        if (res.data) {
            console.log('跳转支付宝支付:', res.data)
            location.href = res.data

            // 支付宝跳转后延迟恢复按钮状态
            setTimeout(() => {
                isDisabled.value = false
                jumpStatus.value = ''
            }, 5000)
        } else {
            proxy.$message.error('获取支付宝支付链接失败')
            isDisabled.value = false
            jumpStatus.value = ''
        }
    }).catch(error => {
        console.error('支付宝支付失败:', error)
        proxy.$message.error('支付宝支付失败，请重试')
        isDisabled.value = false
        jumpStatus.value = ''
    })
}

// 格式化停车时长
function formatParkingDuration(duration) {
    if (!duration || duration <= 0) {
        return '--'
    }

    // 假设duration是分钟数，转换为天小时分钟
    const totalMinutes = Math.floor(duration)
    const days = Math.floor(totalMinutes / (24 * 60))
    const hours = Math.floor((totalMinutes % (24 * 60)) / 60)
    const minutes = totalMinutes % 60

    let result = ''

    if (days > 0) {
        result += `${days}天`
    }
    if (hours > 0) {
        result += `${hours}小时`
    }
    if (minutes > 0) {
        result += `${minutes}分钟`
    }

    // 如果都为0，显示0分钟
    if (!result) {
        result = '0分钟'
    }

    return result
}


</script>
<style lang="scss" scope>
.home {
    height: 100vh;
    overflow: hidden;
    width: 100vw;

    .home_bg {
        height: 100%;
        background-image: url('/src/assets/vip/bg.png');
        background-size: 100%;
        background-repeat: no-repeat;
        position: relative;
        z-index: 1;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .cell {
        width: 90%;
        position: absolute;
        z-index: 2;

        .scan_cell {
            width: 100%;
            border-radius: 20px;
            background-color: #dcecff;
            padding: 0 16px 20px;

            .cell_header {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .scan_cell_title {
                font-size: 28px;
                font-weight: bold;
                line-height: 29px;
                background: linear-gradient(180deg, #205ad3 0%, #2150b3 100%);
                background-clip: text;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
            }

            .scan_cell_img {
                background-image: url('/src/assets/vip/car.png');
                background-size: 100%;
                background-repeat: no-repeat;
                width: 137px;
                height: 97px;
            }
        }

        .cell_input {
            background-image: url('/src/assets/vip/block.png');
            background-size: cover;
            background-repeat: no-repeat;
            width: 100%;
            margin-top: -16px;
            padding: 16px 20px 20px;

            .cell_title {
                font-size: 18px;
                font-weight: bold;
                color: #212121;
                margin-bottom: 10px;
            }

            .cell_item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 14px;
                margin-bottom: 10px;
                color: #666;
            }

            .cell_input_title {
                font-size: 14px;
                font-weight: bold;
                color: #212121;
                margin-bottom: 10px;
            }

            .cell_input_radio {
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .plateNo {
                display: flex;
                justify-content: space-around;
                align-items: center;
                padding-bottom: 10px;
            }
        }

        .submit-btn {
            width: 260px;
            height: 48px;
            line-height: 48px;
            background: linear-gradient(360deg, #ffc234 0%, #fffae3 100%);
            box-shadow: inset 0px -2px 1px 0px #f0a535, inset 0px -4px 4px 0px rgba(255, 255, 255, 0.25),
                0px 4px 15px 0px rgba(209, 202, 185, 0.8);
            border-radius: 200px;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            color: #de5d00;
            margin: 20px auto 0;
        }


    }
}
</style>





