// config.js
const ENV_CONFIG = {
  // 开发环境（本地调试）
  develop: {
    BASE_URL: 'http://**************:8080',
    API_PREFIX: ''
  },
  // 测试环境
  trial: {
    BASE_URL: 'https://test-parknew.lgfw24hours.com:3443',
    API_PREFIX: '/api'
  },
  // 生产环境
  release: {
    BASE_URL: 'https://test-parknew.lgfw24hours.com:3443',
    API_PREFIX: '/api'
  }
};

// 获取小程序环境版本
const env = wx.getAccountInfoSync().miniProgram.envVersion || 'develop';

// 拼接完整URL
const FULL_URL = `${ENV_CONFIG[env].BASE_URL}${ENV_CONFIG[env].API_PREFIX}`;

// 导出完整URL
export const URL = FULL_URL;