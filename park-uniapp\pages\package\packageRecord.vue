<template>
    <view class="package-record">
        <!-- 用户类型筛选 - 仅VIP和集团用户显示 -->
        <view class="user-type-filter" v-if="showUserTypeFilter">
            <view 
                v-for="userType in userTypeFilters" 
                :key="userType.value"
                class="filter-tab" 
                :class="{ active: activeUserTypeFilter === userType.value }" 
                @tap="setUserTypeFilter(userType.value)"
            >
                {{ userType.label }}
            </view>
        </view>

        <!-- 订单状态筛选 -->
        <view class="status-filter"
            :style="{ top: (specialUser && specialUser.userType && (specialUser.userType === 'VIP客户' || specialUser.userType === '集团客户')) ? '88rpx' : '0' }">
            <view 
                v-for="status in statusFilters" 
                :key="status.value"
                class="filter-tab" 
                :class="{ active: activeStatusFilter === status.value }" 
                @tap="setStatusFilter(status.value)"
            >
                {{ status.label }}
            </view>
        </view>

        <view class="content"
            :style="{ paddingTop: (specialUser && specialUser.userType && (specialUser.userType === 'VIP客户' || specialUser.userType === '集团客户')) ? '196rpx' : '108rpx' }">
            <template v-if="recordList.length > 0">
                <view class="record-list">
                    <view class="record-item" v-for="item in recordList" :key="item.id">
                        <view class="record-card">
                            <view class="card-header">
                                <view class="order-info">
                                    <text class="order-no">{{ item.tradeId || '订单号待生成' }}</text>
                                </view>
                                <view class="header-right">
                                    <view class="status-row">
                                        <view class="status-badge" :class="getStatusClass(item.payStatus)">
                                            {{ getStatusText(item.payStatus) }}
                                        </view>
                                    </view>
                                </view>
                            </view>

                            <view class="card-body">
                                <view class="info-item">
                                    <text class="info-label">停车场:</text>
                                    <text class="info-value">{{ item.warehouseName }}</text>
                                    <text class="info-label plate-label">车牌:</text>
                                    <text class="info-value">{{ item.plateNo }}</text>
                                </view>

                                <view class="info-item">
                                    <text class="info-label">会员类型:</text>
                                    <text class="info-value">{{ item.packageName }}</text>
                                </view>

                                <view class="info-item">
                                    <text class="info-label">购买时间:</text>
                                    <text class="info-value time">{{ item.createTime}}</text>
                                </view>

                                <view class="info-item">
                                    <text class="info-label">开始时间:</text>
                                    <text class="info-value time">{{item.beginVipTime}}</text>
                                </view>
                                <view class="info-item">
                                    <text class="info-label">结束时间:</text>
                                    <text class="info-value time">{{item.expirationTime }}</text>
                                </view>

                                <view class="card-bottom">
                                    <view class="price-info">
                                        <view class="price-item" v-if="item.discountAmount > 0">
                                            <text class="price-label">优惠:</text>
                                            <text class="price-value discount">-¥{{ item.discountAmount }}</text>
                                        </view>
                                        <view class="price-item">
                                            <text class="price-label">实付:</text>
                                            <text class="price-value actual">¥{{ item.actualPayment }}</text>
                                        </view>
                                    </view>

                                    <!-- 已支付订单 - 开发票按钮区域 -->
                                    <template v-if="item.payStatus === 5">
                                        <view class="invoice-actions">
                                            <!-- 已开具发票，可发送邮箱 -->
                                            <template
                                                v-if="item.miniInvoiceRecord && item.miniInvoiceRecord.status === 'ISSUED'">
                                                <view class="invoice-btn send-btn"
                                                    @tap="chooseEmail(item.miniInvoiceRecord.id)">
                                                    <up-icon name="email" color="#3b82f6" size="14"></up-icon>
                                                    <text>发送邮箱</text>
                                                </view>
                                            </template>

                                            <!-- 未开具发票，可开发票 -->
                                            <template
                                                v-if="!item.miniInvoiceRecord || (item.miniInvoiceRecord && (item.miniInvoiceRecord.status === 'UNISSU'
                                                 || item.miniInvoiceRecord.status === 'CLOSED') && !item.miniInvoiceRecord.reopenSign)">
                                                <view class="invoice-btn open-btn" @tap="routeToInvoice(item)">
                                                    <up-icon name="file-text" color="#ffffff" size="14"></up-icon>
                                                    <text>开发票</text>
                                                </view>
                                            </template>

                                            <!-- 申请换开 -->
                                            <template
                                                v-if="item.miniInvoiceRecord && ((item.miniInvoiceRecord.status === 'ISSUED' && !item.miniInvoiceRecord.reopenSign) 
                                                || (item.miniInvoiceRecord.status === 'CLOSED' && item.miniInvoiceRecord.reopenSign) || item.miniInvoiceRecord.status === 'REVERSED')">
                                                <view class="invoice-btn reopen-btn" @tap="routeToInvoice(item)">
                                                    <up-icon name="reload" color="#ffffff" size="14"></up-icon>
                                                    <text>申请换开</text>
                                                </view>
                                            </template>
                                        </view>
                                    </template>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </template>

            <template v-else>
                <view class="empty-state">
                    <view class="empty-icon">📋</view>
                    <text class="empty-text">暂无购买记录</text>
                </view>
            </template>
        </view>

        <!-- 邮箱发送弹窗 -->
        <up-popup :show="showPopup" mode="center" :round="10" :safeAreaInsetBottom="false" closeOnClickOverlay
            @close="showPopup = false">
            <view class="popup-cell">
                <view class="email">
                    <view class="content_item">
                        <view class="email-title">电子邮箱</view>
                        <view class="email-input">
                            <up-input v-model="notifyEmail" border="none" placeholder="请输入您的电子邮箱" clearable
                                fontSize="28rpx" color="#616161" :placeholderStyle="placeholderStyle"
                                :customStyle="emailInputStyle"></up-input>
                        </view>
                    </view>
                    <view class="desc">如无特殊情况，我们将于24小时之内将发票发送至您的邮箱。</view>
                </view>
                <view class="choose_btn">
                    <view class="cancel_btn" @tap="showPopup = false">取消</view>
                    <view class="sure_btn" @tap="handlePostInvoiceSend">确认</view>
                </view>
            </view>
        </up-popup>
    </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { getUserPackageRecordList } from '@/api/package';
import { getSpecialUser } from '@/api/specialUser';
import { postInvoiceSend } from '@/api/invoice';
// TODO: 添加取消订单API
// import { cancelPackageOrder } from '@/api/package';

const recordList = ref([]);
const vipType = ref(0);
const specialUser = ref({});
const activeStatusFilter = ref(0); // 订单状态筛选，0代表全部
const activeUserTypeFilter = ref(''); // 用户类型筛选
const showPopup = ref(false);
const invoiceId = ref(null);
const notifyEmail = ref('');

// 订单状态筛选配置
const statusFilters = ref([
    { value: 0, label: '全部' },
    { value: 5, label: '已支付' },
    { value: 3, label: '已失败' },
    { value: 4, label: '已退款' }
]);

// 用户类型筛选配置
const userTypeFilters = ref([
    { value: '普通用户', label: '普通套餐' }
]);

// 样式对象
const placeholderStyle = {
    color: '#616161'
}

const emailInputStyle = {
    paddingLeft: '20rpx',
    paddingTop: '26rpx',
    paddingBottom: '26rpx',
    borderBottom: '1rpx solid rgba(189,189,189,0.2)'
}

// 是否显示用户类型筛选
const showUserTypeFilter = computed(() => {
    return specialUser.value && specialUser.value.userType && 
           (specialUser.value.userType === 'VIP客户' || specialUser.value.userType === '集团客户');
});

onLoad((options) => {
    vipType.value = options.vipType || 0;
});

onShow(() => {
    initUserData();
});

// 初始化用户数据
const initUserData = async () => {
    try {
        const res = await getSpecialUser();
        specialUser.value = res.data || {};
        console.log('specialUser:', specialUser.value);
        
        // 动态设置用户类型筛选选项
        if (specialUser.value && specialUser.value.userType) {
            const userTypeLabel = specialUser.value.userType === 'VIP客户' ? 'VIP套餐' : '集团套餐';
            userTypeFilters.value = [
                { value: '普通用户', label: '普通套餐' },
                { value: specialUser.value.userType, label: userTypeLabel }
            ];
            
            // 设置默认筛选为当前用户类型
            activeUserTypeFilter.value = specialUser.value.userType;
        } else {
            // 普通用户只显示普通套餐
            activeUserTypeFilter.value = '普通用户';
        }
        
        // 获取记录列表
        getRecordList();
    } catch (error) {
        console.error('获取用户信息失败:', error);
        // 即使获取用户信息失败，也要获取记录列表
        activeUserTypeFilter.value = '普通用户';
        getRecordList();
    }
};

// 获取套餐购买记录
const getRecordList = async () => {
    try {
        uni.showLoading({
            title: '加载中...',
            mask: true
        });
        
        // 构建查询参数
        const params = {};
        
        // 优先使用用户筛选的类型，否则使用页面参数传入的类型
        if (activeUserTypeFilter.value) {
            params.vipType = getUserTypeNumber(activeUserTypeFilter.value);
        } else {
            params.vipType = vipType.value;
        }
        
        // 添加订单状态筛选
        if (activeStatusFilter.value !== 0) {
            params.payStatus = activeStatusFilter.value;
        }
        
        console.log('查询参数:', params);
        const res = await getUserPackageRecordList(params);
        recordList.value = res.data || [];
        
    } catch (error) {
        console.error('获取套餐购买记录失败:', error);
        uni.showToast({
            title: '获取记录失败',
            icon: 'none'
        });
    } finally {
        uni.hideLoading();
    }
};

// 设置订单状态筛选
const setStatusFilter = async (payStatus) => {
    if (activeStatusFilter.value === payStatus) return;
    
    activeStatusFilter.value = payStatus;
    await getRecordList();
};

// 设置用户类型筛选
const setUserTypeFilter = async (userType) => {
    if (activeUserTypeFilter.value === userType) return;
    
    activeUserTypeFilter.value = userType;
    await getRecordList();
};


// 格式化日期范围
const formatDateRange = (beginTime, endTime) => {
    return `${beginTime} 至 ${endTime}`;
};

// 用户类型映射配置
const userTypeMap = {
    'VIP客户': 2,
    '集团客户': 1,
    '普通用户': 0
};

// 将用户类型字符串转换为数字
const getUserTypeNumber = (userType) => {
    return userTypeMap[userType] ?? 0;
};

// 获取会员类型文本
const getVipTypeText = (vipType) => {
    // 如果是字符串类型，直接返回
    if (typeof vipType === 'string') {
        return vipType;
    }
    
    // 兼容数字类型
    switch (vipType) {
        case 0:
            return '普通用户';
        case 1:
            return '集团客户';
        case 2:
            return 'VIP客户';
        default:
            return '普通用户';
    }
};

// 状态映射配置
const statusMap = {
    3: { text: '已失败', class: 'status-failed' },
    4: { text: '已退款', class: 'status-failed' },
    5: { text: '已支付', class: 'status-paid' }
};

// 获取支付状态对应的文本
const getStatusText = (status) => {
    return statusMap[status]?.text || '未知状态';
};

// 获取支付状态对应的样式类
const getStatusClass = (status) => {
    return statusMap[status]?.class || 'status-unknown';
};

// 跳转到开发票页面
const routeToInvoice = (item) => {
    // functionType 功能类型：1停车，2会员
    if (item.miniInvoiceRecord && item.miniInvoiceRecord.id) {
        // 发票重开
        if (item.miniInvoiceRecord.isResume) {
            uni.navigateTo({
                url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=2&invoiceId=${item.miniInvoiceRecord.id}&isResume=${item.miniInvoiceRecord.isResume}`
            });
        } else {
            uni.navigateTo({
                url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=2&invoiceId=${item.miniInvoiceRecord.id}`
            });
        }
    } else {
        uni.navigateTo({
            url: `/pages/invoice/openInvoice?functionId=${item.id}&money=${item.actualPayment}&functionType=2`
        });
    }
};

// 选择邮箱发送
const chooseEmail = (id) => {
    invoiceId.value = id;
    showPopup.value = true;
};

// 发送发票到邮箱
const handlePostInvoiceSend = async () => {
    if (!notifyEmail.value) {
        uni.showToast({
            title: '请输入邮箱',
            icon: 'none'
        });
        return;
    }
    
    try {
        const params = {
            id: invoiceId.value,
            notifyEmail: notifyEmail.value
        };
        
        await postInvoiceSend(params);
        
        uni.showToast({
            title: '邮箱发送成功～',
            icon: 'none',
            duration: 2000
        });
        
        setTimeout(() => {
            showPopup.value = false;
            notifyEmail.value = '';
        }, 1000);
    } catch (error) {
        console.error('发送邮箱失败:', error);
        uni.showToast({
            title: '发送失败，请重试',
            icon: 'none'
        });
    }
};


</script>

<style lang="scss" scoped>
.package-record {
    background: #f5f5f5;
    min-height: 100vh;
}

// 用户类型筛选器
.user-type-filter {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: #ffffff;
    z-index: 10;
    display: flex;
    padding: 16rpx 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(36, 107, 253, 0.08);
    
    .filter-tab {
        flex: 1;
        text-align: center;
        padding: 14rpx 12rpx;
        font-size: 26rpx;
        color: #666666;
        border-radius: 12rpx;
        margin: 0 6rpx;
        transition: all 0.3s ease;
        
        &.active {
            background: #3b82f6;
            color: #ffffff;
            font-weight: 500;
        }
        
        &:not(.active):active {
            background: #f8fafc;
        }
        
        &:first-child {
            margin-left: 0;
        }
        
        &:last-child {
            margin-right: 0;
        }
    }
}

// 订单状态筛选器
.status-filter {
    position: fixed;
    left: 0;
    width: 100%;
    background: #ffffff;
    z-index: 9;
    display: flex;
    padding: 16rpx 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(36, 107, 253, 0.08);
    
    .filter-tab {
        flex: 1;
        text-align: center;
        padding: 14rpx 12rpx;
        font-size: 26rpx;
        color: #666666;
        border-radius: 12rpx;
        margin: 0 6rpx;
        transition: all 0.3s ease;
        
        &.active {
            background: #3b82f6;
            color: #ffffff;
            font-weight: 500;
        }
        
        &:not(.active):active {
            background: #f8fafc;
        }
        
        &:first-child {
            margin-left: 0;
        }
        
        &:last-child {
            margin-right: 0;
        }
    }
}

.content {
    padding: 20rpx 32rpx 40rpx;
}

.record-list {
    .record-item {
        margin-bottom: 20rpx;
        
        .record-card {
            background: white;
            border-radius: 20rpx;
            padding: 32rpx;
            box-shadow: 0 8rpx 24rpx rgba(36, 107, 253, 0.1);
            
            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                margin-bottom: 16rpx;
                
                .order-info {
                    .order-no {
                        font-size: 20rpx;
                        font-weight: bold;
                        color: #797979;
                    }
                }
                
                .header-right {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;
                    
                    .status-row {
                        display: flex;
                        align-items: center;
                        gap: 12rpx;
                    }
                    
                    .status-badge {
                        padding: 6rpx 12rpx;
                        border-radius: 16rpx;
                        font-size: 22rpx;
                        font-weight: 500;
                        
                        &.status-failed {
                            background: #fef2f2;
                            color: #dc2626;
                            border: 1rpx solid #fecaca;
                        }
                        
                        &.status-paid {
                            background: #f0f9ff;
                            color: #0ea5e9;
                            border: 1rpx solid #bae6fd;
                        }
                        
                        &.status-unknown {
                            background: #f9fafb;
                            color: #6b7280;
                            border: 1rpx solid #e5e7eb;
                        }
                    }
                }
            }
            
            .card-body {
                .info-item {
                    display: flex;
                    align-items: center;
                    margin-bottom: 16rpx;
                    
                    .info-label {
                        font-size: 26rpx;
                        color: #666666;
                        margin-right: 16rpx;
                        min-width: 120rpx;
                        
                        &.plate-label {
                            margin-left: 40rpx;
                            min-width: 80rpx;
                        }
                    }
                    
                    .info-value {
                        font-size: 26rpx;
                        color: #333333;
                        flex: 1;
                        
                        &.time {
                            color: #333333;
                        }
                    }
                }
                
                .card-bottom {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 20rpx;
                    padding-top: 20rpx;
                    border-top: 1rpx solid #f5f5f5;
                    
                    .price-info {
                        display: flex;
                        align-items: center;
                        
                        .price-item {
                            display: flex;
                            align-items: center;
                            margin-right: 20rpx;
                            
                            .price-label {
                                font-size: 22rpx;
                                color: #666666;
                                margin-right: 8rpx;
                            }
                            
                            .price-value {
                                font-size: 26rpx;
                                font-weight: bold;
                                
                                &.discount {
                                    color: #16a34a;
                                }
                                
                                &.actual {
                                    color: #ff0000;
                                    font-size: 30rpx;
                                }
                            }
                        }
                    }
                    
                    .invoice-actions {
                        display: flex;
                        align-items: center;
                        
                        .invoice-btn {
                            display: flex;
                            align-items: center;
                            padding: 12rpx 16rpx;
                            border-radius: 8rpx;
                            font-size: 24rpx;
                            font-weight: 500;
                            margin-left: 10rpx;
                            
                            text {
                                margin-left: 6rpx;
                            }
                            
                            &.send-btn {
                                background: #f0f9ff;
                                color: #3b82f6;
                                border: 1rpx solid #bae6fd;
                            }
                            
                            &.open-btn {
                                background: #3b82f6;
                                color: #ffffff;
                            }
                            
                            &.reopen-btn {
                                background: #f59e0b;
                                color: #ffffff;
                            }
                        }
                    }
                }
            }
        }
    }
}

.bottom-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40rpx;
    padding: 20rpx 0;
    
    .divider-line {
        width: 100rpx;
        height: 1rpx;
        background: #e0e0e0;
    }
    
    .tip-text {
        font-size: 22rpx;
        color: #666666;
        margin: 0 20rpx;
    }
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0;
    
    .empty-icon {
        font-size: 120rpx;
        margin-bottom: 20rpx;
        opacity: 0.3;
    }
    
    .empty-text {
        font-size: 26rpx;
        color: #666666;
    }
}

.popup-cell {
    width: 600rpx;
    text-align: center;
    padding: 44rpx 0;
    
    .email {
        padding: 32rpx;
        background: #ffffff;
        border-radius: 20rpx;
        
        .content_item {
            margin-bottom: 20rpx;
            
            .email-title {
                font-size: 28rpx;
                color: #212121;
                font-weight: 500;
                margin-bottom: 16rpx;
                text-align: left;
            }
        }
        
        .desc {
            font-size: 24rpx;
            color: #f5820e;
            line-height: 1.5;
            margin-top: 16rpx;
        }
    }
    
    .choose_btn {
        display: flex;
        justify-content: space-around;
        padding: 0 20rpx;
        margin-top: 32rpx;
        
        .cancel_btn, .sure_btn {
            width: 240rpx;
            height: 80rpx;
            line-height: 80rpx;
            border-radius: 80rpx;
            text-align: center;
            font-size: 28rpx;
            font-weight: 600;
        }
        
        .cancel_btn {
            background: #ffffff;
            border: 2rpx solid #246bfd;
            color: #246bfd;
        }
        
        .sure_btn {
            background: #246bfd;
            color: #ffffff;
        }
    }
}
</style>