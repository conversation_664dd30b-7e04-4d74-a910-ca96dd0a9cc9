<template>
    <view class="mine-edit-container">
        <!-- 用户信息编辑卡片 -->
        <view class="edit-card">
            <!-- 头像编辑 -->
            <view class="edit-item-avatar">
                <view class="edit-label-avatar">我的头像</view>
                <view class="avatar-wrapper">
                    <button class="avatar-btn" open-type="chooseAvatar" @chooseavatar="chooseavatar">
                        <image class="avatar-image" :src="userEdit.img || '/static/mine/avatar.png'" mode="aspectFill">
                        </image>
                    </button>
                </view>
            </view>

            <!-- 昵称编辑 -->
            <view class="edit-item-horizontal">
                <view class="edit-label-horizontal">我的昵称</view>
                <view class="content-wrapper">
                    <input type="nickname" v-model="userEdit.nickName" placeholder="请输入昵称" maxlength="12"
                        class="content-text" />
                </view>
            </view>

            <!-- 手机号码编辑 -->
            <view class="edit-item-horizontal">
                <view class="edit-label-horizontal">手机号码</view>
                <view class="phone-wrapper">
                    <!-- 普通用户可以点击修改 -->
                    <template v-if="userEdit.userType === 0 || userEdit.userType === '0'">
                        <button class="phone-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
                            @tap.stop="">
                            <text class="phone-text">{{ userEdit.phoneNumber || '请选择手机号码' }}</text>
                        </button>
                    </template>
                    <!-- 集团客户和VIP用户只显示文本 -->
                    <template v-else>
                        <text class="phone-text" @tap="handlePhoneEdit">{{ userEdit.phoneNumber || '请选择手机号码' }}</text>
                    </template>
                </view>
            </view>
        </view>

        <!-- 保存按钮 -->
        <view class="save-section">
            <button @tap="handleSave" class="save-btn">保存</button>
        </view>
    </view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';
import { getPhoneNumberByCode, updateUserInfo } from '@/api/user';
import { uploadAvatar } from '@/utils/utils';

const userInfo = ref(null);
const isLogin = ref(false);
const isAvatarChanged = ref(false); // 标记头像是否被修改

const userEdit = ref({
    img: '',
    nickName: '',
    phoneNumber: '',
    userType: 0
});

onLoad(() => {
    userInfo.value = uni.getStorageSync('wxUser');

    // 初始化编辑数据
    if (userInfo.value) {
        userEdit.value.img = userInfo.value.img || '';
        userEdit.value.nickName = userInfo.value.nickName || '';
        userEdit.value.phoneNumber = userInfo.value.phoneNumber || '';
        userEdit.value.userType = userInfo.value.userType || 0;  
    }

    if (uni.getStorageSync('token')) {
        isLogin.value = true;
    }
});

// 选择头像
const chooseavatar = (e) => {
    console.log(e);
    // 获取选择的头像URL
    const avatarUrl = e.detail.avatarUrl;
    if (avatarUrl) {
        userEdit.value.img = avatarUrl;
        isAvatarChanged.value = true; // 标记头像已被修改
    }
};

// 获取手机号码
const getPhoneNumber = (e) => {
    const phoneCode = e.detail.code;
    if (phoneCode) {
        getPhoneNumberByCode(phoneCode).then(res => {
            userEdit.value.phoneNumber = res.data
        })
    } else {
        console.log('验证手机号失败')
    }
};

// 处理手机号编辑（非普通用户）
const handlePhoneEdit = () => {
    const userType = userEdit.value.userType;
    
    if (userType === 1) {
        // 集团客户
        uni.showModal({
            title: '温馨提示',
            content: '集团客户修改手机号，请联系客服',
            showCancel: false,
            confirmText: '我知道了'
        });
    } else if (userType === 2) {
        // VIP用户
        uni.showModal({
            title: '温馨提示',
            content: 'VIP客户修改手机号，请联系客服',
            showCancel: false,
            confirmText: '我知道了'
        });
    } else {
        // 未知用户类型，默认不允许修改
        uni.showModal({
            title: '温馨提示',
            content: '当前用户类型无法修改手机号，请联系客服',
            showCancel: false,
            confirmText: '我知道了'
        });
    }
};

// 保存用户信息
const handleSave = async () => {
    // 获取系统缓存中的用户信息
    const cachedUserInfo = uni.getStorageSync('wxUser') || {};

    // 检查是否有变化
    const hasChanges = (
        (userEdit.value.img && userEdit.value.img !== cachedUserInfo.img) ||
        userEdit.value.nickName !== cachedUserInfo.nickName ||
        userEdit.value.phoneNumber !== cachedUserInfo.phoneNumber
    );

    if (!hasChanges) {
        uni.showToast({
            title: '信息未更新',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    // 显示加载提示
    uni.showLoading({
        title: '保存中...',
        mask: true
    });

    try {
        // 如果头像被修改且是临时文件，先上传头像
        let finalImgUrl = userEdit.value.img;
        if (isAvatarChanged.value && userEdit.value.img && (userEdit.value.img.startsWith('wxfile://') || userEdit.value.img.includes('tmp'))) {
            try {
                // 上传头像文件
                const uploadResult = await uploadAvatar(userEdit.value.img);
                finalImgUrl = uploadResult;
                console.log('头像上传成功:', finalImgUrl);
            } catch (error) {
                console.error('头像上传失败:', error);
                uni.hideLoading();
                uni.showToast({
                    title: '头像上传失败',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
        }

        // 准备保存的数据
        const saveData = {
            img: finalImgUrl,
            nickName: userEdit.value.nickName,
            phoneNumber: userEdit.value.phoneNumber,
            userName: userEdit.value.phoneNumber
        };
        console.log(saveData);

        // 这里调用后端接口更新用户信息
        const res = await updateUserInfo(saveData);

        uni.setStorageSync('token', res.data.token)
        uni.setStorageSync('wxUser', res.data.wxUser)
        uni.hideLoading();
        
        // 重置头像修改标记
        isAvatarChanged.value = false;

        uni.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 1500
        });

        // 延迟返回上一页
        setTimeout(() => {
            uni.navigateBack();
        }, 1500);

    } catch (error) {
        console.error('保存失败:', error);
        uni.hideLoading();
        
        // 显示后端返回的具体错误信息
        const errorMsg = error.msg || error.message || '保存失败，请重试';
        uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 2000
        });
    }
};
</script>

<style lang="scss" scoped>
.mine-edit-container {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding: 24rpx;
}

// 编辑卡片
.edit-card {
    background-color: #fff;
    border-radius: 24rpx;
    padding: 20rpx 36rpx 20rpx 28rpx;
    margin-bottom: 24rpx;
}

// 头像编辑
.edit-item-avatar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 120rpx;
    padding: 16rpx 0;
    border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);

    .edit-label-avatar {
        font-size: 32rpx;
        color: #000000;
    }

    .avatar-wrapper {
        display: flex;
        align-items: center;
        gap: 12rpx;
    }

    .avatar-btn {
        width: 120rpx;
        height: 120rpx;
        border: none;
        background: transparent;
        padding: 0;
        margin: 0;
        border-radius: 50%;
        overflow: hidden;

        &::after {
            border: none;
        }
    }

    .avatar-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        border: 4rpx solid #f0f0f0;
    }
}

// 左右布局的编辑项
.edit-item-horizontal {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 120rpx;
    padding: 16rpx 0;
    border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);

    .edit-label-horizontal {
        font-size: 32rpx;
        color: #000000;
        flex-shrink: 0;
        min-width: 160rpx;
    }

    .content-wrapper {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        flex: 1;
        margin-left: 20rpx;
        gap: 12rpx;
    }

    .content-text {
        font-size: 32rpx;
        color: #000000;
        text-align: right;
        border: none;
        background: transparent;
        flex: 1;
        
        &::placeholder {
            color: #999999;
        }
    }

    .phone-wrapper {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        flex: 1;
        margin-left: 20rpx;
        gap: 12rpx;
    }

    .phone-btn {
        background: transparent;
        padding: 0;
        margin: 0;
        font-size: 32rpx;
        color: #333333;
        line-height: 42rpx;
        border: none;
        text-align: right;

        &::after {
            border: none;
        }

        .phone-text {
            color: #000000;
        }
    }

    .phone-text {
        color: #000000;
        font-size: 32rpx;
        text-align: right;
    }
}



.save-section {
    padding-top: 20rpx;
}

.save-btn {
    width: 100%;
    height: 88rpx;
    // background: linear-gradient(135deg, #246bfd 0%, #6f9eff 100%);
    background: linear-gradient(90deg, #4BA1FC 0%, hsl(240, 100%, 78%) 100%);
    color: #fff;
    border-radius: 44rpx;
    font-size: 32rpx;
    border: none;
}
</style>
