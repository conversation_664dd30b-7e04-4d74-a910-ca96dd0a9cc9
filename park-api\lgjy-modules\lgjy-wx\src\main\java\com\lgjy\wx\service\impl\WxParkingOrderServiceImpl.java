package com.lgjy.wx.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.constant.SecurityConstants;
import com.lgjy.common.core.domain.R;
import com.lgjy.common.core.exception.ServiceException;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.core.utils.snow.SnowflakeIdGenerator;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.security.utils.SecurityUtils;
import com.lgjy.system.api.RemoteGateService;
import com.lgjy.system.api.domain.WxUser;
import com.lgjy.wx.constants.PayConstants;
import com.lgjy.wx.domain.*;
import com.lgjy.wx.mapper.*;
import com.lgjy.wx.rt.UnionPayApiRestTemplate;
import com.lgjy.wx.rt.UnionPayUtil;
import com.lgjy.wx.rt.config.UnionPayApiProperties;
import com.lgjy.wx.service.WxParkingOrderService;
import com.lgjy.wx.utils.DateUtils;
import com.lgjy.wx.utils.TemporaryPlateNoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class WxParkingOrderServiceImpl implements WxParkingOrderService {

    @Resource
    private RemoteGateService remoteGateService;

    @Resource
    private WxParkingOrderMapper wxParkingOrderMapper;

    @Resource
    private UnionPayUtil unionPayUtil;

    @Resource
    private WxUnionPayConfigMapper wxUnionPayConfigMapper;

    @Resource
    private UnionPayApiRestTemplate unionPayApiRestTemplate;

    @Resource
    private WxOrderRefundMapper wxOrderRefundMapper;

    @Resource
    private UnionPayApiProperties unionPayApiProperties;

    @Resource
    private WxUserMapper wxUserMapper;

    @Resource
    private SnowflakeIdGenerator snowflakeIdGenerator;

    @Resource
    private WxWareHouseMapper wxWareHouseMapper;

    @Resource
    private WxInvoiceRecordMapper wxInvoiceRecordMapper;

    /**
     * 根据车牌号查询订单
     * 
     * @param wxParkingOrder
     * @return
     */
    @Override
    public WxParkingOrder getParkingOrderByPlateNo(WxParkingOrder wxParkingOrder) {
        // 查询参数，车牌号,场库ID
        if (StringUtils.isEmpty(wxParkingOrder.getPlateNo()) || wxParkingOrder.getWarehouseId() == null) {
            throw new ServiceException("订单查询参数错误");
        }

        // 获取场库信息
        WxWareHouse warehouse = wxWareHouseMapper.getWarehouseById(wxParkingOrder.getWarehouseId());
        if(ObjectUtils.isEmpty(warehouse)){
            throw new ServiceException("场库信息不存在");
        }

        // 根据车牌和场库id调查道闸订单金额
        calculateOrderOnline(wxParkingOrder);

        // 数据库里查已有却未支付订单，如果有则给出id
        wxParkingOrder.setPayStatus(PayConstants.ORDER_PAY_STATUS_PROGRESS);
        List<WxParkingOrder> wxParkingOrderList = wxParkingOrderMapper.selectWxParkingOrderList(wxParkingOrder);
        if (CollectionUtils.isNotEmpty(wxParkingOrderList)) {
            wxParkingOrder.setId(wxParkingOrderList.get(0).getId());
        }
        wxParkingOrder.setWarehouseName(warehouse.getWarehouseName());
        return wxParkingOrder;
    }

    /**
     * 查询当前用户的临停订单列表
     * @return
     */
    @Override
    public List<WxParkingOrder> getParkingOrderList(WxParkingOrder orderParams) {
        Long userId = SecurityUtils.getUserId();
        WxUser wxUser = wxUserMapper.selectUserById(userId);
        if(wxUser == null){
            throw new ServiceException("用户信息不存在");
        }
        // 全部，不加条件
        if(orderParams.getPayStatus()==0){
            orderParams.setPayStatus(null);
        }
        orderParams.setUserId(userId);
        List<WxParkingOrder> wxParkingOrderList = wxParkingOrderMapper.selectWxParkingOrderList(orderParams);
        // 查询订单是否有开发票记录
        for(WxParkingOrder order : wxParkingOrderList){
            WxInvoiceRecord invoiceRecord = new WxInvoiceRecord();
            // 如果还在进行中，则查询最新的记录
            if(order.getPayStatus()==PayConstants.ORDER_PAY_STATUS_PROGRESS){
                calculateOrderOnline(order);
            }
            if(Objects.equals(order.getPayStatus(), PayConstants.ORDER_PAY_STATUS_PAID)){
                invoiceRecord.setStatus(PayConstants.INVOICE_STATUS_UNISSU);
            }
            if (Objects.nonNull(order.getInvoiceId())) {
                invoiceRecord = wxInvoiceRecordMapper.selectSicvInvoiceRecordById(order.getInvoiceId());
                if (Objects.nonNull(invoiceRecord) && ((Objects.equals(invoiceRecord.getStatus(), PayConstants.INVOICE_STATUS_ISSUED) && Objects.equals(invoiceRecord.getReopenSign(), false))
                        || (Objects.equals(invoiceRecord.getStatus(), PayConstants.INVOICE_STATUS_CLOSED) && Objects.equals(invoiceRecord.getReopenSign(), true))
                        || (Objects.equals(invoiceRecord.getStatus(), PayConstants.INVOICE_STATUS_REVERSED)))) {
                    invoiceRecord.setIsResume(true);
                }
            }
            order.setMiniInvoiceRecord(invoiceRecord);
        }
        return wxParkingOrderList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public JSONObject createParkingOrder(WxParkingOrder wxParkingOrder) {
        try{
            // 1. 参数校验与用户信息获取
            if (wxParkingOrder == null || wxParkingOrder.getWarehouseId() == null || StringUtils.isEmpty(wxParkingOrder.getPlateNo())) {
                throw new ServiceException("订单参数不合法");
            }
            WxUser wxUser = wxUserMapper.selectUserById(SecurityUtils.getUserId());
            if (wxUser == null || StringUtils.isEmpty(wxUser.getOpenId())) {
                throw new ServiceException("用户信息不完整");
            }

            // 2. 创建最终订单
            WxParkingOrder order = new WxParkingOrder();
            order.setUserId(SecurityUtils.getUserId());
            order.setWarehouseId(wxParkingOrder.getWarehouseId());
            order.setPlateNo(wxParkingOrder.getPlateNo());
            order.setPayStatus(PayConstants.ORDER_PAY_STATUS_PROGRESS);
            order.setCarType(PayConstants.CAR_TYPE_TEMPORARY_CAR);
            order.setTradeId(unionPayUtil.generateOrderNo());
            order.setPayType(PayConstants.PAY_TYPE_WE_CHAT);

            // 3. 计算金额并校验
            calculateOrderOnline(order);
            if (order.getPaymentAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ServiceException("订单金额必须大于0");
            }

            // 4. 保存订单(查询有旧订单则更新)
            int row = 0;
            if(wxParkingOrder.getId() != null){
                order.setId(wxParkingOrder.getId());
                order.setUpdateTime(new Date());
                row=wxParkingOrderMapper.updateWxParkingOrder(order);
            }else{
                order.setId(snowflakeIdGenerator.nextId());
                order.setCreateTime(new Date());
                order.setUpdateTime(new Date());
                row=wxParkingOrderMapper.insertWxParkingOrder(order);
            }
            if (row <= 0) {
                throw new ServiceException("订单保存失败");
            }

            // 5. 支付处理
            WxUnionPayConfig payConfig = wxUnionPayConfigMapper.selectWxUnionPayConfigList(
                    wxParkingOrder.getWarehouseId(), PayConstants.INVOICE_FUNCTION_TYPE_PARK);
            if (payConfig == null) {
                throw new ServiceException("支付配置未找到");
            }

            JSONObject payResult = unionPayApiRestTemplate.jsapi(
                    order.getTradeId(),
                    order.getActualPayment(),
                    new Date(),
                    unionPayApiProperties.getNotifyUrlOrder(),
                    wxUser.getOpenId(),
                    null,
                    payConfig.getMid(),
                    payConfig.getTid()
            );

            if (!PayConstants.WE_CHAT_PAY_SUCCESS.equals(payResult.getString("errCode"))) {
                throw new ServiceException("微信支付失败: " + payResult.getString("errMsg"));
            }

            // 6. 构造返回结果
            JSONObject response = JSON.parseObject(JSON.toJSONString(payResult.get("miniPayRequest")));
            response.put("needPay", true);
            response.put("tradeId", order.getTradeId());
            return response;
        }catch(Exception e){
            throw new ServiceException("订单创建失败"+e.getMessage());
        }
    }

    /**
     * 银联支付回调(微信)
     * @param body
     */
    @Override
    public void payCallback(JSONObject body) {
        String tradeId = body.getString("merOrderId");
        if(StringUtils.isEmpty(tradeId)){
            log.error("回调参数错误，订单号："+tradeId+"不存在");
            return;
        }
        WxParkingOrder wxParkingOrder = wxParkingOrderMapper.selectWxParkingOrderByTradeId(tradeId);
        // 修改订单  支付时间，支付状态，类型
        if(!ObjectUtils.isEmpty(wxParkingOrder)&&!Objects.equals(wxParkingOrder.getPayStatus(), PayConstants.ORDER_PAY_STATUS_PAID)){
            wxParkingOrder.setPaymentTime(new Date());
            wxParkingOrder.setPayStatus(PayConstants.ORDER_PAY_STATUS_PAID);
            wxParkingOrder.setPayType(PayConstants.PAY_TYPE_WE_CHAT);
            wxParkingOrder.setUpdateTime(new Date());
            int update = wxParkingOrderMapper.updateWxParkingOrder(wxParkingOrder);
            if(update >= 0) {
                // 支付成功，通知道闸
                JSONObject requestData = new JSONObject();
                if (wxParkingOrder.getPlateNo().contains("临")){
                    if(StringUtils.isEmpty(body.getString("srcReverse"))){
                        log.error("临牌支付成功，srcReverse为空");
                    }
                    requestData.put("gateNo", body.getString("srcReverse"));
                    requestData.put("isNoPlate", PayConstants.IS_NO_PLATE_CAR);
                }
                requestData.put("paymentAmount", wxParkingOrder.getPaymentAmount());
                requestData.put("parkingId", wxParkingOrder.getWarehouseId().toString());
                requestData.put("payType", PayConstants.PAY_TYPE_WE_CHAT);
                requestData.put("plateNo", wxParkingOrder.getPlateNo());
                requestData.put("tradeId", tradeId);
                R<String> result=remoteGateService.payOrder(requestData, SecurityConstants.INNER);
                if(ObjectUtils.isEmpty(result) || R.isError(result)){
                    log.error(tradeId + "支付成功，通知道闸失败：{}", result.getMsg());
                }
            }
        }
    }

    /**
     * 前端支付成功-回调
     * @param tradeId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void payFrontPayCallback(String tradeId) {
        log.info("前端支付成功回调-临停停车订单支付回调");
        WxParkingOrder wxParkingOrder = wxParkingOrderMapper.selectWxParkingOrderByTradeId(tradeId);
        // 订单不为空，且订单状态不是已完成才处理
        if(!ObjectUtils.isEmpty(wxParkingOrder)&&!Objects.equals(wxParkingOrder.getPayStatus(), PayConstants.ORDER_PAY_STATUS_PAID)){
            // 银联查询订单信息
            WxUnionPayConfig payConfig = wxUnionPayConfigMapper.selectWxUnionPayConfigList(
                    wxParkingOrder.getWarehouseId(), PayConstants.INVOICE_FUNCTION_TYPE_PARK);
            if(payConfig == null){
                throw new ServiceException("支付参数异常");
            }
            try{
                JSONObject jsonObject = unionPayApiRestTemplate.queryOrderByMchNo(tradeId, new Date(), PayConstants.ORDER_INST_MID_MINI,
                        payConfig.getMid(), payConfig.getTid());
                if(!PayConstants.WE_CHAT_PAY_SUCCESS.equals(jsonObject.getString("errCode"))){
                    throw new ServiceException("银联查询订单失败");
                }
                // 更新订单信息
                wxParkingOrder.setPaymentTime(new Date());
                wxParkingOrder.setPayStatus(PayConstants.ORDER_PAY_STATUS_PAID);
                wxParkingOrder.setPayType(PayConstants.PAY_TYPE_WE_CHAT);
                int update = wxParkingOrderMapper.updateWxParkingOrder(wxParkingOrder);
                if(update >= 0){
                    // 通知道闸
                    JSONObject requestData = new JSONObject();
                    if (wxParkingOrder.getPlateNo().contains("临")){
                        requestData.put("gateNo", jsonObject.getString("srcReverse"));
                        requestData.put("isNoPlate", PayConstants.IS_NO_PLATE_CAR);
                    }
                    requestData.put("paymentAmount", wxParkingOrder.getPaymentAmount());
                    requestData.put("parkingId", wxParkingOrder.getWarehouseId().toString());
                    requestData.put("payType", PayConstants.PAY_TYPE_WE_CHAT);
                    requestData.put("plateNo", wxParkingOrder.getPlateNo());
                    requestData.put("tradeId", tradeId);
                    R<String> result=remoteGateService.payOrder(requestData, SecurityConstants.INNER);
                    if(ObjectUtils.isEmpty(result) && R.isError(result)){
                        log.error(tradeId + "支付成功，通知道闸失败：{}", result.getMsg());
                    }
                }
            }catch(Exception e){
                log.error("前端支付回调失败" + e);
            }
        }
    }

    /**
     * 银联支付回调(支付宝)
     * @param body
     */
    @Override
    public void payCallbackAlipay(JSONObject body) {
        String tradeId = body.getString("merOrderId");
        WxParkingOrder wxParkingOrder = wxParkingOrderMapper.selectWxParkingOrderByTradeId(tradeId);
        if(!ObjectUtils.isEmpty(wxParkingOrder)&&!Objects.equals(wxParkingOrder.getPayStatus(), PayConstants.ORDER_PAY_STATUS_PAID)){
            wxParkingOrder.setPaymentTime(new Date());
            wxParkingOrder.setPayStatus(PayConstants.ORDER_PAY_STATUS_PAID);
            wxParkingOrder.setPayType(PayConstants.PAY_TYPE_WE_CHAT);
            int update = wxParkingOrderMapper.updateWxParkingOrder(wxParkingOrder);
            if(update >= 0) {
                JSONObject requestData = new JSONObject();
                requestData.put("paymentAmount", wxParkingOrder.getPaymentAmount());
                requestData.put("parkingId", wxParkingOrder.getWarehouseId().toString());
                requestData.put("payType", PayConstants.PAY_TYPE_WE_CHAT);
                requestData.put("plateNo", wxParkingOrder.getPlateNo());
                requestData.put("tradeId", tradeId);
                R<String> result=remoteGateService.payOrder(requestData, SecurityConstants.INNER);
                if(ObjectUtils.isEmpty(result) && R.isError(result)){
                    throw new ServiceException(result.getMsg()==null?"查询订单失败":result.getMsg());
                }
            }
        }
    }



    /**
     * 道闸口出口查询订单
     */
    @Override
    public WxParkingOrder channelPayQuery(WxParkingOrder wxParkingOrder) {
        // 检验参数
        String parkingId = wxParkingOrder.getWarehouseId().toString();
        Integer gateNo = wxParkingOrder.getGateNo();
        if(StringUtils.isEmpty(parkingId)|| gateNo==null){
            throw new ServiceException("参数错误");
        }
        // 道闸查询
        JSONObject requestData = new JSONObject();
        requestData.put("parkingId", parkingId);
        requestData.put("gateNo", gateNo);
        R<String> response = remoteGateService.outPayQuery(requestData, SecurityConstants.INNER);
        if(R.isError(response)){
            throw new ServiceException(response.getMsg()==null ? "查询订单失败" : response.getMsg());
        }
        JSONObject result = JSONObject.parseObject(response.getData());
        wxParkingOrder.setPlateNo(result.getString("plateNum"));
        // 根据场库和车牌号查询是否有创建过但未支付(1)订单，已关联出场库名字
        List<WxParkingOrder> wxParkingOrders = wxParkingOrderMapper.selectWxParkingOrderList(wxParkingOrder);
        if(!CollectionUtils.isEmpty(wxParkingOrders)){
            wxParkingOrder = wxParkingOrders.get(0);
        }
        WxWareHouse wxWareHouse = wxWareHouseMapper.getWarehouseById(Long.valueOf(parkingId));
        if(wxWareHouse == null){
            throw new ServiceException("场库不存在");
        }
        wxParkingOrder.setWarehouseName(wxWareHouse.getWarehouseName());
        wxParkingOrder.setBeginParkingTime(result.getDate("inTime"));
        wxParkingOrder.setEndParkingTime(result.getDate("outTime"));
        wxParkingOrder.setPaymentAmount(new BigDecimal(result.getString("needPayAmount")));
        wxParkingOrder.setParkingDuration((int) (wxParkingOrder.getEndParkingTime().getTime()-wxParkingOrder.getBeginParkingTime().getTime())/ (1000 * 60));
        return wxParkingOrder;
    }

    /**
     * 临停支付-支付宝
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String paymentTemporaryAlipay(WxParkingOrder wxParkingOrder) {
        WxParkingOrder old=temporaryOrder(wxParkingOrder, PayConstants.PAY_TYPE_ALIPAY);
        WxUnionPayConfig payConfig = wxUnionPayConfigMapper.selectWxUnionPayConfigList(
                old.getWarehouseId(), PayConstants.INVOICE_FUNCTION_TYPE_PARK);
        if(ObjectUtils.isEmpty(payConfig)){
            throw new ServiceException("银联支付配置丢失");
        }

        String url="";
        try {
            url = unionPayUtil.h5payAlipay(old.getTradeId(), new Date(), old.getPaymentAmount(), old.getPlateNo(), unionPayApiProperties.getNotifyUrlOrderAlipay(),
                    payConfig.getMid(), payConfig.getTid());
        }catch (Exception e){
            log.error("支付宝h5银联支付异常", e);
        }

        log.info("临停缴费-支付宝H5调用地址：{}", url);
        return url;
    }

    /**
     * 临停缴费-微信H5 预下单
     */
    @Override
    public JSONObject paymentTemporary(WxParkingOrder wxParkingOrder) {
        WxParkingOrder old=temporaryOrder(wxParkingOrder, PayConstants.PAY_TYPE_WE_CHAT);
        WxUnionPayConfig payConfig = wxUnionPayConfigMapper.selectWxUnionPayConfigList(
                old.getWarehouseId(), PayConstants.INVOICE_FUNCTION_TYPE_PARK);
        if(ObjectUtils.isEmpty(payConfig)){
            throw new ServiceException("银联支付配置丢失");
        }
        JSONObject jsonObject = null;
        try {
            jsonObject = unionPayApiRestTemplate.jsapi(old.getTradeId(), old.getPaymentAmount(), new Date(),
                    unionPayApiProperties.getNotifyUrlOrder(), wxParkingOrder.getOpenId(), null,
                    payConfig.getMid(),payConfig.getTid());
        } catch (Exception e) {
            throw new ServiceException("微信预下单失败"+e);
        }
        JSONObject miniPayRequest = JSONObject.parseObject(JSON.toJSONString(jsonObject.get("miniPayRequest")));
        miniPayRequest.put("tradeId", old.getTradeId());
        return miniPayRequest;
    }

    /**
     * 无牌车入场
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void noPlateIn(WxParkingOrder wxParkingOrder) {
        Long warehouseId = wxParkingOrder.getWarehouseId();
        String openId = wxParkingOrder.getOpenId();
        Integer gateNo = wxParkingOrder.getGateNo();
        if(warehouseId == null || openId == null || gateNo == null){
            throw new ServiceException("参数错误,请重新扫码");
        }
        // 查询当前用户微信，无牌进入未完成订单
        wxParkingOrder.setPayStatus(PayConstants.ORDER_PAY_STATUS_PROGRESS);
        List<WxParkingOrder> wxParkingOrders = wxParkingOrderMapper.selectWxParkingOrderList(wxParkingOrder);
        // 如果有，删除旧订单（同一个微信号进入的未完成的）
        if(!CollectionUtils.isEmpty(wxParkingOrders)){
            wxParkingOrderMapper.deleteWxParkingOrderByIds(wxParkingOrders.stream().map(WxParkingOrder::getId).toArray(Long[]::new));
        }
        String temporaryPlateNo = TemporaryPlateNoUtils.temporaryPlateNo();
        wxParkingOrder.setPlateNo(temporaryPlateNo);
        // 调用道闸入场
        JSONObject requestData = new JSONObject();
        requestData.put("parkingId", warehouseId.toString());
        requestData.put("plateNo", temporaryPlateNo);
        requestData.put("gateNo", gateNo);
        R<String> response= remoteGateService.noPlateIn(requestData, SecurityConstants.INNER);
        if(R.isError(response)){
            throw new ServiceException(response.getMsg()==null ? "无牌入场失败" : response.getMsg());
        }
        wxParkingOrder.setId(snowflakeIdGenerator.nextId());
        wxParkingOrder.setCarType("临时车");
        wxParkingOrder.setBeginParkingTime(new Date());
        wxParkingOrder.setTradeId(unionPayUtil.generateOrderNo());
        wxParkingOrderMapper.insertWxParkingOrder(wxParkingOrder);
    }
    /**
     * 无牌车出场，预下单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public AjaxResult noPlateOut(WxParkingOrder wxParkingOrder) {
        Long warehouseId = wxParkingOrder.getWarehouseId();
        String openId = wxParkingOrder.getOpenId();
        Integer gateNo = wxParkingOrder.getGateNo();
        if(warehouseId == null || openId == null || gateNo == null){
            throw new ServiceException("参数错误");
        }
        // 查询当前用户微信，无牌进入未完成订单
        wxParkingOrder.setPayStatus(PayConstants.ORDER_PAY_STATUS_PROGRESS);
        List<WxParkingOrder> wxParkingOrders = wxParkingOrderMapper.selectWxParkingOrderList(wxParkingOrder);
        if(CollectionUtils.isEmpty(wxParkingOrders)){
            throw new ServiceException("车辆不在场");
        }
        WxParkingOrder order = wxParkingOrders.get(0);
        // 查询道闸计算金额
        calculateOrderOnline(order);
        order.setTradeId(unionPayUtil.generateOrderNo());
        order.setPayType(PayConstants.PAY_TYPE_WE_CHAT);
        // 如果订单金额为0，则直接出场
        if (order.getPaymentAmount().compareTo(BigDecimal.ZERO) == 0) {
            // 更新订单状态
            order.setPaymentTime(new Date());
            order.setPayStatus(PayConstants.ORDER_PAY_STATUS_PAID);
            int update=wxParkingOrderMapper.updateWxParkingOrder(order);
            if(update<=0){
                throw new ServiceException("更新订单失败");
            }
            JSONObject requestData = new JSONObject();
            requestData.put("parkingId", order.getWarehouseId().toString());
            requestData.put("tradeId", order.getTradeId());
            requestData.put("plateNo", order.getPlateNo());
            requestData.put("payType", PayConstants.PAY_TYPE_WE_CHAT);
            requestData.put("gateNo", String.valueOf(gateNo));
            requestData.put("isNoPlate", PayConstants.IS_NO_PLATE_CAR);
            requestData.put("paymentAmount", order.getPaymentAmount());
            // 通知道闸
            R<String> response = remoteGateService.payOrder(requestData, SecurityConstants.INNER);
            if (R.isError(response)) {
                throw new ServiceException(response.getMsg()==null ? "查询订单失败" : response.getMsg());
            }
            return AjaxResult.success("000",null);
        }
        int update = wxParkingOrderMapper.updateWxParkingOrder(order);
        if(update<=0){
            throw new ServiceException("更新订单失败");
        }
        WxUnionPayConfig wxUnionPayConfig = wxUnionPayConfigMapper.selectWxUnionPayConfigList(order.getWarehouseId(), PayConstants.INVOICE_FUNCTION_TYPE_PARK);
        if(ObjectUtils.isEmpty(wxUnionPayConfig)){
            throw new ServiceException("银联支付配置丢失");
        }
        JSONObject jsonObject = null;
        try {
            jsonObject = unionPayApiRestTemplate.jsapi(order.getTradeId(), order.getPaymentAmount(), new Date(),
                    unionPayApiProperties.getNotifyUrlOrder(), openId, String.valueOf(gateNo),
                    wxUnionPayConfig.getMid(), wxUnionPayConfig.getTid());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        JSONObject miniPayRequest = JSONObject.parseObject(JSON.toJSONString(jsonObject.get("miniPayRequest")));
        miniPayRequest.put("tradeId", order.getTradeId());
        return AjaxResult.success("1",miniPayRequest);
    }



    public WxParkingOrder temporaryOrder(WxParkingOrder wxParkingOrder, int payType){
        // 检验场库号和道闸编号
        String parkingId = wxParkingOrder.getWarehouseId().toString();
        String gateNo = wxParkingOrder.getGateNo().toString();
        if(StringUtils.isEmpty(parkingId)|| StringUtils.isEmpty(gateNo)){
            throw new ServiceException("参数错误");
        }

        // 出口查询停车费
        JSONObject requestData = new JSONObject();
        requestData.put("parkingId", parkingId);
        requestData.put("gateNo", gateNo);
        R<String> response = remoteGateService.outPayQuery(requestData, SecurityConstants.INNER);
        if(response.getData()==null || R.isError(response)){
            throw new ServiceException(response.getMsg()==null ? "查询订单失败" : response.getMsg());
        }
        JSONObject result = JSONObject.parseObject(response.getData());
        wxParkingOrder.setPlateNo(result.getString("plateNum"));
        wxParkingOrder.setPayStatus(PayConstants.ORDER_PAY_STATUS_PROGRESS);
        // 根据场库和车牌号查询是否有创建过但未支付(1)订单
        List<WxParkingOrder> orderList = wxParkingOrderMapper.selectWxParkingOrderList(wxParkingOrder);
        WxParkingOrder old = new WxParkingOrder();
        // 有未支付订单,银联关闭H5订单
        if(!CollectionUtils.isEmpty(orderList)){
            old=orderList.get(0);
            // 未支付订单是支付宝的
            if(StringUtils.isNotEmpty(old.getTradeId())&&Objects.equals(old.getPayType(), PayConstants.PAY_TYPE_ALIPAY)){
                WxUnionPayConfig payConfig = wxUnionPayConfigMapper.selectWxUnionPayConfigList(
                        old.getWarehouseId(), PayConstants.INVOICE_FUNCTION_TYPE_PARK);
                if(ObjectUtils.isEmpty(payConfig)){
                    throw new ServiceException("银联支付配置丢失");
                }
                try{
                    JSONObject res=unionPayApiRestTemplate.closeAlipayOrder(old.getTradeId(), new Date(), PayConstants.ORDER_INST_MID_H5,
                            payConfig.getMid(), payConfig.getTid());
                    log.info("关闭支付宝订单结果：{}", res);
                }catch (Exception e){
                    log.error("关闭订单异常：{}", e.getMessage());
                }
            }
            // 未支付订单是微信小程序的
            else if(StringUtils.isNotEmpty(old.getTradeId())&&Objects.equals(old.getPayType(), PayConstants.PAY_TYPE_WE_CHAT)){
                WxUnionPayConfig payConfig = wxUnionPayConfigMapper.selectWxUnionPayConfigList(
                        old.getWarehouseId(), PayConstants.INVOICE_FUNCTION_TYPE_PARK);
                if(ObjectUtils.isEmpty(payConfig)){
                    throw new ServiceException("银联支付配置丢失");
                }
                try{
                    JSONObject res=unionPayApiRestTemplate.closeWeChatOrder(old.getTradeId(), new Date(), PayConstants.ORDER_INST_MID_MINI,
                            payConfig.getMid(), payConfig.getTid());
                    log.info("关闭微信订单结果：{}", res);
                }catch (Exception e){
                    log.error("关闭订单异常：{}", e.getMessage());
                }
            }
        }
        // 无未支付订单
        else{
            old.setWarehouseId(wxParkingOrder.getWarehouseId());
            old.setPlateNo(wxParkingOrder.getPlateNo());
            try{
                old.setBeginParkingTime(DateUtils.parse(result.get("inTime").toString(),DateUtils.DATE_HOUR_MINUTE_SECOND_FORMATTER));
            }catch(Exception e){
                log.error("解析时间异常：{}", e.getMessage());
            }
            old.setPayStatus(PayConstants.ORDER_PAY_STATUS_PROGRESS);
            old.setCarType("临时车");
        }
        try{
            old.setEndParkingTime(DateUtils.parse(result.get("outTime").toString(),DateUtils.DATE_HOUR_MINUTE_SECOND_FORMATTER));
        }catch(Exception e){
            log.error("解析时间异常：{}", e.getMessage());
        }
        BigDecimal needPayAmount= new BigDecimal(result.get("needPayAmount").toString());
        int amount = DateUtils.intervalMinutes(old.getBeginParkingTime(), old.getEndParkingTime());
        old.setParkingDuration(amount);
        old.setPaymentAmount(needPayAmount);
        old.setDiscountAmount(BigDecimal.ZERO);
        old.setActualPayment(needPayAmount);
        old.setTradeId(unionPayUtil.generateOrderNo());
        old.setPayType(payType);
        old.setPaymentTime(new Date());
        old.setOpenId(wxParkingOrder.getOpenId());
        if(old.getPaymentAmount().compareTo(BigDecimal.ZERO)<=0){
            throw new ServiceException("订单金额为0元，无需支付");
        }
        int update = 0;
        // 4. 保存订单
        if(old.getId()==null){
            old.setId(snowflakeIdGenerator.nextId());
            update = wxParkingOrderMapper.insertWxParkingOrder(old);
        }else{
            update = wxParkingOrderMapper.updateWxParkingOrder(old);
        }
        if (update <= 0) {
            throw new ServiceException("订单保存失败");
        }
        return old;
    }

    /**
     * 支付订单查询在场车辆
     */
    private void calculateOrderOnline(WxParkingOrder wxParkingOrder) {
        // 根据场库id和车牌查询订单金额
        String parkingId = wxParkingOrder.getWarehouseId().toString();
        String plateNum = wxParkingOrder.getPlateNo();
        JSONObject requestData = new JSONObject();
        requestData.put("parkingId", parkingId);
        requestData.put("plateNum", plateNum);
        R<String> response = remoteGateService.findParkingRate(requestData, SecurityConstants.INNER);
        if (R.isError(response) || ObjectUtils.isEmpty(response.getData())) {
            throw new ServiceException(StringUtils.isNotEmpty(response.getMsg())?response.getMsg(): "订单查询失败");
        }

        JSONObject json = JSONObject.parseObject(response.getData());
        // 需要支付金额
        BigDecimal needPayAmount = json.getBigDecimal("needPayAmount");
        if (needPayAmount==null) {
            throw new ServiceException("未获取到在场车辆订单信息或该订单已支付，无需支付");
        }
        // 入场时间
        Date beginParkingTime = new Date(json.getLongValue("inTime")* 1000L);
        // 查询时，当前时间为出场时间
        Date endParkingTime = new Date();
        // 计算停车时长，向上取整
        int amount = (int) Math.ceil((double) (endParkingTime.getTime() - beginParkingTime.getTime()) / (1000 * 60));
        wxParkingOrder.setBeginParkingTime(beginParkingTime);
        wxParkingOrder.setEndParkingTime(endParkingTime);
        wxParkingOrder.setParkingDuration(Math.max(amount, 0));
        wxParkingOrder.setPaymentAmount(needPayAmount);
        wxParkingOrder.setDiscountAmount(BigDecimal.ZERO);
        wxParkingOrder.setActualPayment(needPayAmount);
    }

    /**
     * 停车订单退款
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject refundParkingOrder(String tradeId, BigDecimal refundAmount, String refundReason) {
        // 1. 查询订单信息
        WxParkingOrder queryOrder = new WxParkingOrder();
        queryOrder.setTradeId(tradeId);
        List<WxParkingOrder> orders = wxParkingOrderMapper.selectWxParkingOrderList(queryOrder);

        if (CollectionUtils.isEmpty(orders)) {
            throw new ServiceException("订单不存在");
        }

        WxParkingOrder order = orders.get(0);

        // 2. 验证订单状态（支付成功才能退款）
        if (order.getPayStatus() == null || !order.getPayStatus().equals(PayConstants.ORDER_PAY_STATUS_PAID)) {
            throw new ServiceException("订单状态不支持退款");
        }

        // 3. 验证退款金额
        if (refundAmount.compareTo(order.getActualPayment()) > 0) {
            throw new ServiceException("退款金额不能大于实付金额");
        }

        // 4. 获取银联支付配置
        WxUnionPayConfig payConfig = wxUnionPayConfigMapper.selectWxUnionPayConfigList(
                order.getWarehouseId(), PayConstants.INVOICE_FUNCTION_TYPE_PARK);
        if (ObjectUtils.isEmpty(payConfig)) {
            throw new ServiceException("银联支付配置丢失");
        }

        // 5. 调用银联退款接口
        JSONObject refundResult;
        try {
            refundResult = unionPayApiRestTemplate.jsapiRefund(
                    tradeId,
                    refundAmount,
                    new Date(),
                    payConfig.getMid(),
                    payConfig.getTid()
            );
        } catch (Exception e) {
            log.error("银联退款接口调用失败", e);
            throw new ServiceException("退款失败：" + e.getMessage());
        }

        // 6. 处理退款结果
        if (refundResult != null && "SUCCESS".equals(refundResult.getString("errCode"))) {
            // 退款成功，更新订单状态
            order.setPayStatus(PayConstants.ORDER_PAY_STATUS_REFUND);
            order.setUpdateTime(new Date());
            wxParkingOrderMapper.updateWxParkingOrder(order);

            // 创建退款记录
            WxOrderRefund refundRecord = new WxOrderRefund();
            refundRecord.setTradeId(tradeId);
            // 退款原因可以为空，如果为空则不设置（数据库字段允许NULL）
            refundRecord.setRefundReason(refundReason != null && !refundReason.trim().isEmpty() ? refundReason : null);
            refundRecord.setRefundType(1); // 1-临停订单
            refundRecord.setOriginalAmount(order.getActualPayment());
            refundRecord.setRefundAmount(refundAmount);
            refundRecord.setCreateTime(new Date());
            wxOrderRefundMapper.insertWxOrderRefund(refundRecord);

            log.info("订单退款成功，订单号：{}，退款金额：{}，退款原因：{}", tradeId, refundAmount, refundReason);
        } else {
            throw new ServiceException("退款失败：" + refundResult.getString("respMsg"));
        }

        return refundResult;
    }
}
