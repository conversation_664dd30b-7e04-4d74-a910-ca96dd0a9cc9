package com.lgjy.wx.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.web.controller.BaseController;
import com.lgjy.common.core.web.domain.AjaxResult;
import com.lgjy.common.security.annotation.InnerAuth;
import com.lgjy.wx.domain.WxParkingOrder;
import com.lgjy.wx.service.WxParkingOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/parking/order")
public class WxParkingOrderController extends BaseController {

    @Resource
    private WxParkingOrderService wxParkingOrderService;

    /**
     * 根据车牌号，场库id查询订单
     * @param wxParkingOrder
     * @return
     */
    @PostMapping("/plateNo")
    public AjaxResult getParkingOrderByPlateNo(@RequestBody WxParkingOrder wxParkingOrder){
        return success(wxParkingOrderService.getParkingOrderByPlateNo(wxParkingOrder));
    }

    /**
     * 查询当前用户的临停订单列表
     * @return
     */
    @PostMapping("/list")
    public AjaxResult getParkingOrderList(@RequestBody WxParkingOrder orderParams){
        return success(wxParkingOrderService.getParkingOrderList(orderParams));
    }

    /**
     * 停车缴费预下单(创建订单）
     * @param wxParkingOrder
     * @return
     */
    @PostMapping("/create")
    public AjaxResult createParkingOrder(@RequestBody WxParkingOrder wxParkingOrder){
        return success(wxParkingOrderService.createParkingOrder(wxParkingOrder));
    }

    /**
     * 小程序停车缴费支付成功，供银联回调(微信)
     */
    @PostMapping("/payCallback")
    public Map<String, Object> payCallback(HttpServletRequest request, HttpServletResponse response) {
        JSONObject body = new JSONObject();
        Map<String, String[]> map = request.getParameterMap();
        for (String key : map.keySet()) {
            body.put(key, map.get(key)[0]);
        }
        log.info("停车订单支付回调start---------------body:{}", JSON.toJSONString(body));
        try {
            wxParkingOrderService.payCallback(body);
            log.info("停车订单支付回调end---------------");
        } catch (Exception e) {
            log.error("临停缴费支付回调失败", e);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("code", "SUCCESS");
        result.put("message", "");
        return result;
    }

    /**
     * 前端支付成功-回调
     * @param wxParkingOrder
     * @return
     */
    @PostMapping("/front/payCallback")
    public AjaxResult payFrontPayCallback(@RequestBody WxParkingOrder wxParkingOrder) {
        try {
            wxParkingOrderService.payFrontPayCallback(wxParkingOrder.getTradeId());
        } catch (Exception e) {
            log.error("前端支付成功-回调异常！", e);
        }
        return success();
    }

    /**
     * 停车订单支付回调，银联支付回调(支付宝h5)
     */
    @PostMapping("/payCallbackAlipay")
    public Map<String, Object> payCallbackAlipay(HttpServletRequest request, HttpServletResponse response) {
        JSONObject body = new JSONObject();
        Map<String, String[]> map = request.getParameterMap();
        for (String key : map.keySet()) {
            body.put(key, map.get(key)[0]);
        }
        log.info("停车订单支付回调start---------------body:{}", JSON.toJSONString(body));
        try {
            wxParkingOrderService.payCallbackAlipay(body);
            log.info("停车订单支付回调end---------------");
        } catch (Exception e) {
            log.error("临停缴费支付宝H5支付回调失败", e);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("code", "SUCCESS");
        result.put("message", "");
        return result;
    }

    /**
     * 道闸口出口查询订单
     */
    @PostMapping("/channelPayQuery")
    public AjaxResult channelPayQuery(@RequestBody WxParkingOrder wxParkingOrder) {
        return success(wxParkingOrderService.channelPayQuery(wxParkingOrder));
    }

    /**
     * 临停缴费-支付宝H5
     */
    @PostMapping("/paymentTemporaryAlipay")
    public AjaxResult paymentTemporaryAlipay(@RequestBody WxParkingOrder wxParkingOrder) {
        try {
            String payUrl = wxParkingOrderService.paymentTemporaryAlipay(wxParkingOrder);
            return AjaxResult.success("获取支付链接成功", payUrl);
        } catch (Exception e) {
            log.error("临停缴费支付宝预下单失败", e);
            return error(e.getMessage());
        }
    }
    /**
     * 临停缴费-微信H5 预下单
     */
    @PostMapping("/paymentTemporary")
    public AjaxResult paymentTemporary(@RequestBody WxParkingOrder wxParkingOrder) {
        try {
            return success(wxParkingOrderService.paymentTemporary(wxParkingOrder));
        } catch (Exception e) {
            log.error("临停缴费支付宝预下单失败", e);
            return error(e.getMessage());
        }
    }

    /**
     * 无牌车 入场
     */
    @PostMapping("/noPlateIn")
    public AjaxResult noPlateIn(@RequestBody WxParkingOrder wxParkingOrder){
        wxParkingOrderService.noPlateIn(wxParkingOrder);
        return success();
    }

    /**
     * 无牌车 出场 预下单
     */
    @PostMapping("/noPlateOut")
    public AjaxResult noPlateOut(@RequestBody WxParkingOrder wxParkingOrder){
        return wxParkingOrderService.noPlateOut(wxParkingOrder);
    }

    /**
     * 停车订单退款
     */
    @InnerAuth
    @PostMapping("/refund")
    public AjaxResult refundParkingOrder(@RequestParam String tradeId, @RequestParam BigDecimal refundAmount, @RequestParam String refundReason) {
        try {
            JSONObject result = wxParkingOrderService.refundParkingOrder(tradeId, refundAmount, refundReason);
            return success("退款成功", result);
        } catch (Exception e) {
            log.error("停车订单退款失败", e);
            return error(e.getMessage());
        }
    }

}
