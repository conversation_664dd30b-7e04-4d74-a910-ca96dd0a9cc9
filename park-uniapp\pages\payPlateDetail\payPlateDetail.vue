<template>
    <view class="pay-plate-detail">
        <view class="cell">
            <view class="top-cell">
                <view class="top-cell-title">
                    <view class="title"> 停车信息确认 </view>
                    <view class="desc"> Information confirmed </view>
                </view>
                <image src="/static/image/carRight.png" mode="aspectFit"></image>
            </view>
            <view class="form_cell">
                <view class="form_cell_top">
                    <view class="form_cell_top_plateNo">
                        <image src="/static/order/car.png" mode="aspectFit"></image>
                        <text class="tips">{{ plateNo }}</text>
                    </view>
                </view>
                <view class="form_cell_word">
                    <image src="/static/package/changku.png" mode="aspectFit"></image>
                    场库信息：<text class="tips">{{ orderInfo.warehouseName || '--' }}</text>
                </view>
                <view class="form_cell_word">
                    <image src="/static/package/kaitong.png" mode="aspectFit"></image>
                    开始时间：<text class="tips">{{ orderInfo.beginParkingTime || '--' }}</text>
                </view>
                <view class="form_cell_word">
                    <image src="/static/package/kaitong.png" mode="aspectFit"></image>
                    结束时间：<text class="tips">{{ orderInfo.endParkingTime || '--' }}</text>
                </view>
                <view class="form_cell_word">
                    <image src="/static/package/kaitong.png" mode="aspectFit"></image>
                    停车时长：<text class="tips">{{ orderInfo.parkingDuration || '--' }} 分钟</text>
                </view>
                <view class="form_cell_word">
                    <image src="/static/package/shifu.png" mode="aspectFit"></image>
                    支付状态：<text class="tips">{{ chenkPayStatus(orderInfo.payStatus)}}</text>
                </view>
                <view class="form_cell_word">
                    <image src="/static/package/taocanprice.png" mode="aspectFit"></image>
                    订单金额：<text class="tips">{{ orderInfo.paymentAmount || '--' }}元</text>
                </view>
                <view class="form_cell_bottom">
                    <text class="word">实付金额：</text>
                    <text class="number">
                        <text class="tips"> ￥ </text>
                        {{ orderInfo.actualPayment || '--' }}</text>
                </view>
            </view>
            <view>
                <button class="submit-btn" @tap="handleCreateParkingOrder"
                    :disabled="orderInfo.payStatus === 5 || orderInfo.paymentAmount === 0 || !orderInfo.paymentAmount">
                    立即支付
                </button>
                <view class="payment-notice">
                    当前页面缴费后，15分钟内出场，不再计费，否则重新计费
                </view>
            </view>
        </view>
    </view>
</template>
<script setup>
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getParkingOrderDetail, createParkingOrder, payParkingOrderCallBack } from '@/api/parkingOrder.js'

// 响应式数据
const orderInfo = ref({})
const plateNo = ref('')
const warehouseId = ref(0)
// 页面加载时查询订单详情
onLoad((options) => {
    plateNo.value = options.plateNo
    warehouseId.value = options.warehouseId
    fetchParkingOrderDetail()
})

// 查询订单详情
const fetchParkingOrderDetail = () => {
    uni.showLoading({
        title: '加载中...',
        mask: true
    })
    if (!plateNo.value || !warehouseId.value) {
        return
    }
    let params = {
        plateNo: plateNo.value,
        warehouseId: warehouseId.value
    }
    getParkingOrderDetail(params).then(res => {
        orderInfo.value = res.data
        uni.hideLoading()
    })
}

// 支付状态
const chenkPayStatus = (status) => {
    switch (status) {
        case 1:
            return '进行中'
        // case 2:
        //     return '支付中'
        case 5:
            return '已支付'
        default:
            return '--'
    }
}

// 创建停车订单
const handleCreateParkingOrder = () => {
    uni.showLoading({
        title: '加载中...',
        mask: true
    })
    createParkingOrder(orderInfo.value).then(res => {
        console.log('创建停车订单 res: ', res)
        if (res.data.needPay) {
            uni.requestPayment({
                timeStamp: res.data.timeStamp,
                nonceStr: res.data.nonceStr,
                package: res.data.package,
                signType: res.data.signType,
                paySign: res.data.paySign,
                success: function (result) {
                    // 延迟一下显示toast，避免与complete中的hideLoading冲突
                    uni.hideLoading()
                    setTimeout(() => {
                        uni.showToast({
                            title: '支付成功~',
                            icon: 'none',
                            duration: 2000
                        })
                    }, 100)
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 2000)
                    // let params = {
                    //     tradeId: res.data.tradeId
                    // }
                    // payParkingOrderCallBack(params).then(item => {
                    //     console.log('停车订单 支付成功回调 item: ', item)
                    // })
                },
                fail: function (err) {
                    uni.hideLoading()
                    console.log('支付失败的回调：', err)
                    // 延迟一下显示toast，避免与hideLoading冲突
                    setTimeout(() => {
                        uni.showToast({
                            title: '支付失败',
                            icon: 'none',
                            duration: 1500
                        })
                    }, 100)
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 2000)
                },
                complete: function (res) {
                    uni.hideLoading()
                }
            })
        }
    })
}
</script>
<style lang="scss" scoped>
.submit-btn {
    margin-top: 40rpx;
    width: 100%;
    background: linear-gradient(90deg, #4BA1FC 0%, #9b8eff 100%);;
    border-radius: 44rpx;
    color: #fff;
    font-size: 32rpx;
    border: none;

    &[disabled] {
        background: #cccccc;
        color: #999999;
    }
}

.payment-notice {
    margin-top: 20rpx;
    text-align: center;
    font-size: 24rpx;
    color: #999999;
    line-height: 1.4;
}

.pay-plate-detail {
    height: 100vh;
    background-color: #f5f5f5;

    .cell {
        padding: 40rpx 32rpx 0;

        .top-cell {
            margin-bottom: 20rpx;
            display: flex;
            align-items: center;

            .top-cell-title {
                margin-right: 8rpx;

                .title {
                    font-size: 40rpx;
                    font-weight: bold;
                    color: #212121;
                    margin-bottom: 8rpx;
                }

                .desc {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #9e9e9e;
                }
            }

            image {
                width: 284rpx;
                height: 200rpx;
            }
        }

        .form_cell {
            padding: 32rpx;
            border-radius: 20rpx;
            background-color: #fff;

            .form_cell_top {
                margin-bottom: 32rpx;

                .form_cell_top_plateNo {
                    display: flex;
                    align-items: center;
                    font-size: 36rpx;
                    font-weight: bold;
                    color: #212121;

                    image {
                        width: 48rpx;
                        height: 48rpx;
                        margin-right: 12rpx;
                    }
                }

                .form_cell_top_edit {
                    padding: 4rpx 12rpx;
                    background: #e9f0ff;
                    border-radius: 4rpx;
                    font-size: 20rpx;
                    font-weight: 400;
                    color: #246bfd;

                    image {
                        width: 32rpx;
                        height: 32rpx;
                        margin-right: 8rpx;
                    }
                }
            }

            .form_cell_word {
                display: flex;
                align-items: center;
                font-size: 28rpx;
                font-weight: 400;
                color: #9e9e9e;
                margin-bottom: 32rpx;

                image {
                    width: 36rpx;
                    height: 36rpx;
                    margin-right: 12rpx;
                }

                .tips {
                    font-size: 28rpx;
                    font-weight: 400;
                    color: #616161;
                }
            }

            .dikou {
                .unit {
                    font-size: 24rpx;
                    margin-right: 8rpx;
                    color: #ff922e;
                    padding-top: 8rpx;
                }

                .desc {
                    font-size: 28rpx;
                    margin-right: 20rpx;
                    font-weight: 400;
                    color: #ff922e;
                }

                .price {
                    color: #ff922e;
                    font-size: 36rpx;
                    font-weight: Bold;
                }

                image {
                    width: 28rpx;
                    height: 28rpx;
                    margin-left: 10rpx;
                }
            }

            .form_cell_bottom {
                border-top: 2rpx solid rgba(189, 189, 189, 0.2);
                padding-top: 16rpx;
                text-align: right;

                .word {
                    font-size: 28rpx;
                    font-weight: bold;
                    color: #212121;
                }

                .number {
                    font-size: 48rpx;
                    font-weight: bold;
                    color: #f90355;

                    .tips {
                        font-size: 28rpx;
                        font-weight: bold;
                        color: #f90355;
                    }
                }
            }
        }
    }
}
</style>
