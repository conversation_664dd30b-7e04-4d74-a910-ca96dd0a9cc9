<template>
    <view class="my-car-add-container">
        <!-- 头部区域 -->
        <view class="header-section">
            <view class="header-content u-flex u-flex-y-center">
                <view class="header-title">
                    <view class="title">{{ isEdit ? '编辑车辆信息' : '添加车辆信息' }}</view>
                    <view class="desc">{{ isEdit ? 'Edit Vehicle Information' : 'Add Vehicle Information' }}</view>
                </view>
                <image src="/static/image/carRight.png" mode="aspectFit" class="header-image"></image>
            </view>
        </view>

        <!-- 表单区域 -->
        <view class="form-section">
            <!-- 车牌号 -->
            <view class="form-item">
                <view class="form-label">车牌号</view>
                <view class="form-input" @tap="showPlateInput">
                    <view class="plate-display">
                        {{ formData.plateNo || '请选择车牌号' }}
                    </view>
                    <u-icon name="arrow-right" size="22" color="#999"></u-icon>
                </view>
            </view>

            <!-- 车型 -->
            <view class="form-item">
                <view class="form-label">车型</view>
                <view class="form-input" @tap="showCarTypePicker">
                    <view class="picker-input">
                        {{ formData.carType || '请选择车型' }}
                    </view>
                    <u-icon name="arrow-right" size="22" color="#999"></u-icon>
                </view>
            </view>

            <!-- 能源类型 -->
            <view class="form-item">
                <view class="form-label">能源类型</view>
                <view class="form-input" @tap="showEnergyTypePicker">
                    <view class="picker-input">
                        {{ getEnergyTypeLabel(formData.energyType) || '请选择能源类型' }}
                    </view>
                    <u-icon name="arrow-right" size="22" color="#999"></u-icon>
                </view>
            </view>

            <!-- 品牌 -->
            <view class="form-item">
                <view class="form-label">品牌</view>
                <view class="form-input">
                    <input v-model="formData.carBrand" placeholder="请输入品牌" class="input-field" maxlength="20" />
                    <u-icon name="arrow-right" size="22" color="#999"></u-icon>
                </view>
            </view>
        </view>

        <!-- 底部按钮 -->
        <view class="bottom-section">
            <view class="submit-btn" @click="handleSubmit">
                <text class="submit-text">{{ isEdit ? '保存修改' : '添加车辆' }}</text>
            </view>
        </view>

        <!-- 车型选择器 -->
        <u-picker :show="showCarType" :columns="[carTypeOptions]" @confirm="onCarTypeConfirm"
            @cancel="showCarType = false"></u-picker>

        <!-- 能源类型选择器 -->
        <u-picker :show="showEnergyType" :columns="[energyTypeOptions.map(item => item.label)]"
            @confirm="onEnergyTypeConfirm" @cancel="showEnergyType = false" :default-index="[0]"></u-picker>

        <!-- 车牌号输入弹窗 -->
        <uni-plate-input v-if="showPlateInputFlag" :plate="formData.plateNo" @close="closePlateInput"
            @export="onPlateConfirm" />
    </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import UniPlateInput from '@/components/uni-plate-input/uni-plate-input.vue';
import { addCar, editCar, getCarDetailById } from '@/api/car';

// 表单数据
const formData = ref({
    plateNo: '',
    carType: '',
    energyType: 0,
    carBrand: ''
});

// 编辑模式标识
const isEdit = ref(false);

onLoad((options) => {
    if (options.isEdit === 'true') {
        isEdit.value = true;
    }
    if (options.id) {
        loadCarInfo(options.id);
    }
});

// 选择器显示状态
const showCarType = ref(false);
const showEnergyType = ref(false);
const showPlateInputFlag = ref(false);

// 车型选项
const carTypeOptions = [
    '微型车',
    '轿车',
    'SUV',
    '其他'
];

// 能源类型选项
const energyTypeOptions = [
    { label: '燃油', value: 1 },
    { label: '纯电', value: 2 },
    { label: '混动', value: 3 }
];

// 能源类型显示映射
const getEnergyTypeLabel = (value) => {
    const option = energyTypeOptions.find(item => item.value === value);
    return option ? option.label : '';
};

// 加载车辆信息
const loadCarInfo = (id) => {
    getCarDetailById({ id: id }).then((res) => {
        if (res.code === 200) {
            formData.value = res.data;
        }
    });
};

// 显示车型选择器
const showCarTypePicker = () => {
    showCarType.value = true;
};

// 显示能源类型选择器
const showEnergyTypePicker = () => {
    showEnergyType.value = true;
};

// 车型选择确认
const onCarTypeConfirm = (e) => {
    formData.value.carType = e.value[0];
    showCarType.value = false;
};

// 能源类型选择确认
const onEnergyTypeConfirm = (e) => {
    const selectedLabel = e.value[0];
    const selectedOption = energyTypeOptions.find(item => item.label === selectedLabel);
    formData.value.energyType = selectedOption ? selectedOption.value : null;
    showEnergyType.value = false;
};

// 显示车牌号输入
const showPlateInput = () => {
    showPlateInputFlag.value = true;
};

// 关闭车牌号输入
const closePlateInput = () => {
    showPlateInputFlag.value = false;
};

// 车牌号确认
const onPlateConfirm = (plate) => {
    formData.value.plateNo = plate;
    showPlateInputFlag.value = false;
};

// 提交表单
const handleSubmit = async () => {
    // 表单验证
    if (!formData.value.plateNo) {
        uni.showToast({
            title: '请输入车牌号',
            icon: 'none'
        });
        return;
    }

    if (!formData.value.carType) {
        uni.showToast({
            title: '请选择车型',
            icon: 'none'
        });
        return;
    }

    if (!formData.value.energyType) {
        uni.showToast({
            title: '请选择能源类型',
            icon: 'none'
        });
        return;
    }
    // 显示加载状态
    uni.showLoading({
        title: isEdit.value ? '保存中...' : '添加中...',
        mask: true
    });

    try {
        let res;
        // 编辑模式
        if (isEdit.value) {
            res = await editCar(formData.value);
        }
        // 添加模式
        else {
            res = await addCar(formData.value);
        }

        uni.hideLoading();

        if (res.code === 200) {
            uni.showToast({
                title: isEdit.value ? '编辑成功' : '添加成功',
                icon: 'success',
                duration: 1500
            });
            
            // 只有成功时才返回上一页
            setTimeout(() => {
                uni.navigateBack();
            }, 1500);
        } else {
            // 显示后端返回的具体错误信息，不返回页面
            uni.showToast({
                title: res.msg || '操作失败',
                icon: 'none',
                duration: 3000
            });
        }
    } catch (error) {
        console.error('操作失败:', error);
        uni.hideLoading();
        
        // 显示捕获到的异常信息
        const errorMsg = error.msg || error.message || '操作失败，请重试';
        uni.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 3000
        });
    }
};
</script>

<style lang="scss" scoped>
.my-car-add-container {
    min-height: 100vh;
    background-color: #f5f5f5;
}

.header-section {
    padding: 20rpx 32rpx 0;

    .header-content {
        .header-title {
            margin-right: 20rpx;

            .title {
                font-size: 38rpx;
                font-weight: bold;
                margin-bottom: 16rpx;
            }

            .desc {
                font-size: 26rpx;
                opacity: 0.5;
            }
        }

        .header-image {
            width: 280rpx;
            height: 200rpx;
        }
    }
}

.form-section {
    background: #fff;
    margin: 0 24rpx 24rpx 24rpx;
    border-radius: 24rpx;
    padding: 32rpx;

    .form-item {
        margin-bottom: 40rpx;

        &:last-child {
            margin-bottom: 0;
        }

        .form-label {
            font-size: 32rpx;
            font-weight: bold;
            color: #000000;
            margin-bottom: 20rpx;
        }

        .form-input {
            display: flex;
            align-items: center;
            padding: 20rpx 0;
            border-bottom: 1rpx solid #dbdbdb;

            .picker-input {
                flex: 1;
                font-size: 32rpx;
                color: #555;
            }

            .plate-display {
                flex: 1;
                font-size: 32rpx;
                color: #555;

            }

            .input-field {
                flex: 1;
                font-size: 32rpx;
                color: #555;
            }
        }
    }
}

.bottom-section {
    margin: 32rpx 24rpx;

    .submit-btn {
        background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
        border-radius: 50rpx;
        padding: 28rpx;
        text-align: center;

        .submit-text {
            color: #fff;
            font-size: 32rpx;
            font-weight: 500;
        }
    }
}

.my-car-add-container {
    min-height: 100vh;
    background-color: #f5f5f5;
}
</style>
