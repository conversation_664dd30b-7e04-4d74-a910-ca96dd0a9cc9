<template>
    <view class="plate-query-container">
        <view class="cell">
            <view class="top-cell u-flex u-flex-y-center">
                <view class="top-cell-title">
                    <view class="title"> 停车缴费 </view>
                    <view class="desc"> Parking payment </view>
                </view>
                <image src="/static/image/carRight.png" mode="aspectFit"></image>
            </view>
            <view class="form-cell">
                <!-- 场库选择器 -->
                <view class="warehouse-selector-section">
                    <view class="form-cell-title">当前场库</view>
                    <view class="warehouse-selector" @tap="showWarehouseSelector">
                        <text class="warehouse-name">{{ currentWarehouse.name || '请选择场库' }}</text>
                        <u-icon name="arrow-down" size="14" color="#999"></u-icon>
                    </view>
                </view>

                <view class="form-cell-title"> 车牌号 </view>
                <view class="so-plate-body" @tap="plateShow = true">
                    <view class="so-plate-word">
                        <text>{{ plateNo.substr(0, 1) }}</text>
                    </view>
                    <view class="so-plate-word">
                        <text>{{ plateNo.substr(1, 1) }}</text>
                    </view>
                    <view class="so-plate-dot"></view>
                    <view class="so-plate-word">
                        <text>{{ plateNo.substr(2, 1) }}</text>
                    </view>
                    <view class="so-plate-word">
                        <text>{{ plateNo.substr(3, 1) }}</text>
                    </view>
                    <view class="so-plate-word">
                        <text>{{ plateNo.substr(4, 1) }}</text>
                    </view>
                    <view class="so-plate-word">
                        <text>{{ plateNo.substr(5, 1) }}</text>
                    </view>
                    <view class="so-plate-word">
                        <text>{{ plateNo.substr(6, 1) }}</text>
                    </view>
                    <template v-if="PageCur == '2'">
                        <view class="so-plate-word">
                            <text>{{ plateNo.substr(7, 1) }}</text>
                        </view>
                    </template>
                </view>
                <view class="form-cell-title u-flex u-flex-y-center u-flex-between">
                    历史纪录
                    <u-icon name="trash" size="24" color="#999" @click="deleteHistory"></u-icon>
                </view>
                <template v-if="plateNoHistoryList.length > 0">
                    <u-grid :border="false" col="3">
                        <u-grid-item v-for="item in plateNoHistoryList" :key="item" @click="handleChoosePlateNo(item)">
                            <view class="center_cell_top_block">
                                <view class="package_name"> {{ item }} </view>
                            </view>
                        </u-grid-item>
                    </u-grid>
                </template>
                <template v-else>
                    <view>
                        <u-empty text="暂无历史记录" mode="history" iconSize="50"></u-empty>
                    </view>
                </template>
            </view>
        </view>

        <view>
            <button @tap="handleRoutePage" class="search-btn">下一步</button>
        </view>

        <!-- 自定义键盘组件 -->
        <plate-input @typeChange="typeChange" v-if="plateShow" :plate="plateNo" @export="setPlate"
            @close="plateShow = false & close()" />

        <!-- 场库选择器组件 -->
        <WarehouseSelector 
            :show="showSelector" 
            :warehouseList="wareHouseList" 
            :currentWarehouse="currentWarehouse"
            :windowHeightHalf="400"
            @close="closeWarehouseSelector" 
            @select="selectWarehouse" 
        />
    </view>
</template>
<script setup>
import { ref, watch } from 'vue'
import {onShow } from '@dcloudio/uni-app'
import plateInput from '@/components/uni-plate-input/uni-plate-input.vue'
import WarehouseSelector from '@/components/warehouse-selector/warehouse-selector.vue'
import { getParkWareHouseList } from '@/api/warehouse'

// 响应式数据
const plateNo = ref('')
const plateShow = ref(false)
const PageCur = ref(1)
const isEdit = ref(false)
const plateNoHistoryList = ref([])

// 场库选择器相关数据
const wareHouseList = ref([])
const showSelector = ref(false)
const currentWarehouse = ref({ id: 0, name: "选择场库" })

// 监听车牌号变化
watch(plateNo, (newVal) => {
    if (newVal.length === 7) {
        PageCur.value = 1
    }
    if (newVal.length === 8) {
        PageCur.value = 2
    }
})

// 页面显示时
onShow(() => {
    plateNoHistoryList.value = uni.getStorageSync('plateNoHistoryList') || []
    initWarehouseData()
})

// 方法
const setPlate = (plate) => {
    if (plate.length >= 7) plateNo.value = plate
    plateShow.value = false
}

const typeChange = (e) => {
    PageCur.value = e
    plateNo.value = ''
}

const close = () => {
    if (!isEdit.value) {
        PageCur.value = 1
    }
}

const handleChoosePlateNo = (plateNoValue) => {
    plateNo.value = plateNoValue
}

// 初始化场库数据
const initWarehouseData = async () => {
    try {
        const res = await getParkWareHouseList()
        wareHouseList.value = res.data.map(item => ({
            id: item.id,
            name: item.warehouseName,
            latitude: item.latitude,
            longitude: item.longitude
        }))

        // 先从缓存中查找场库信息
        const cachedWarehouse = uni.getStorageSync('currentWarehouse')
        if (cachedWarehouse && wareHouseList.value.some(w => w.id === cachedWarehouse.id)) {
            currentWarehouse.value = cachedWarehouse
        } else if (wareHouseList.value.length > 0) {
            // 如果缓存中没有，使用场库列表的第一项
            currentWarehouse.value = wareHouseList.value[0]
            uni.setStorageSync('currentWarehouse', currentWarehouse.value)
        }
    } catch (error) {
        uni.showToast({
            title: '场库数据加载失败',
            icon: 'none'
        })
    }
}

// 显示场库选择器
const showWarehouseSelector = () => {
    showSelector.value = true
}

// 关闭场库选择器
const closeWarehouseSelector = () => {
    showSelector.value = false
}

// 选择场库
const selectWarehouse = (warehouse) => {
    currentWarehouse.value = warehouse
    uni.setStorageSync('currentWarehouse', currentWarehouse.value)
    closeWarehouseSelector()
}

const handleRoutePage = () => {
    if (!plateNo.value) {
        return uni.showToast({
            title: '请选择车牌~',
            duration: 2000,
            icon: 'none'
        })
    }
    
    // 检查是否选择了场库
    if (!currentWarehouse.value || !currentWarehouse.value.id || currentWarehouse.value.id === 0) {
        return uni.showToast({
            title: '请选择场库~',
            duration: 2000,
            icon: 'none'
        })
    }
    
    let flag = true
    plateNoHistoryList.value.forEach(item => {
        if (plateNo.value === item) {
            flag = false
        }
    })
    if (flag) {
        // 最多只显示六条历史记录
        if (plateNoHistoryList.value.length === 6) {
            plateNoHistoryList.value.pop()
        }
        plateNoHistoryList.value.unshift(plateNo.value)
        uni.setStorageSync('plateNoHistoryList', plateNoHistoryList.value)
    }
    uni.navigateTo({
        url: '/pages/payPlateDetail/payPlateDetail?plateNo=' + plateNo.value + '&warehouseId=' + currentWarehouse.value.id
    })
}

const deleteHistory = () => {
    uni.showModal({
        title: '提示',
        content: '确认要清空历史记录吗?',
        success(res) {
            if (res.confirm) {
                uni.removeStorageSync('plateNoHistoryList')
                setTimeout(() => {
                    plateNoHistoryList.value = uni.getStorageSync('plateNoHistoryList') || []
                }, 300)
            }
        }
    })
}

</script>
<style lang="scss" scoped>
.plate-query-container {
    background-color: #f5f5f5;
    height: 100vh;
}

.warehouse-selector-section {
    margin-bottom: 20rpx;
}

.warehouse-selector {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 25rpx 0;
}

.warehouse-name {
    font-size: 32rpx;
    color: #333;
    flex: 1;
}

.cell {
    padding: 40rpx 32rpx 0;

    .top-cell {
        margin-bottom: 20rpx;

        .top-cell-title {
            margin-right: 8rpx;

            .title {
                font-size: 40rpx;
                font-weight: bold;
                color: #212121;
                margin-bottom: 8rpx;
            }

            .desc {
                font-size: 28rpx;
                font-weight: 400;
                color: #9e9e9e;
            }
        }

        image {
            width: 284rpx;
            height: 200rpx;
        }
    }

    .form-cell {
        padding: 32rpx;
        border-radius: 20rpx;
        background-color: #fff;

        .form-cell-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #000000;
            margin-bottom: 20rpx;

            image {
                width: 36rpx;
                height: 36rpx;
            }
        }

        .center_cell_top_block {
            background: #f3f5f7;
            border-radius: 200rpx;
            padding: 16rpx 0 18rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #9e9e9e;
            text-align: center;
            word-break: keep-all;
            margin-bottom: 20rpx;
            width: 95%;
        }
    }
}

.so-plate-body {
    box-sizing: border-box;
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    align-items: center;
    margin-bottom: 40rpx;
}

.so-plate-word {
    border: 1rpx solid #246bfd;
    border-radius: 10upx;
    height: 88rpx;
    margin: 0 5upx;
    box-sizing: border-box;
    width: 71rpx;
    position: relative;
}

.so-plate-word text {
    position: absolute;
    top: 50%;
    left: 50%;
    -webkit-transform: translateX(-50%) translateY(-50%);
    transform: translateX(-50%) translateY(-50%);
    font-weight: 700;
    font-size: 32upx;
}

.so-plate-dot {
    width: 15rpx;
    height: 15rpx;
    background: #246bfd;
    border-radius: 50%;
    margin: 0 5rpx;
}

.search-btn {
    margin-top: 40rpx;
    width: 85%;
    // background-color: #4986ff;
    // background: linear-gradient(135deg, #667eea 0%, #8d66b4 100%);
    background: linear-gradient(90deg, #4BA1FC 0%, #9b8eff 100%);
    border-radius: 44rpx;
    color: #fff;
    font-size: 32rpx;
}
</style>
