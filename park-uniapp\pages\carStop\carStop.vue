<template>
    <view class="car-stop-container">
        <view class="cell">
            <view class="top-cell u-flex u-flex-y-center">
                <view class="top-cell-title">
                    <view class="title"> 停车缴费 </view>
                    <view class="desc"> Parking payment </view>
                </view>
                <image src="/static/image/plateQuery.png" mode="aspectFit"></image>
            </view>
        </view>
    </view>
</template>
<script setup>
import { ref } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getOpenid } from "@/api/login";
import { paymentTemporary } from "@/api/parkingOrder";

const gateNo = ref(null);
const warehouse = ref(null);
const openid = ref(null);

onLoad((options) => {
    console.log('小程序接收到的参数:', options);

    let stopNoValue = null;
    
    // 方案1：直接传参（原有逻辑）
    if (options.stopNo) {
        stopNoValue = options.stopNo;
        console.log('直接传参模式，stopNo:', stopNoValue);
    }
    // 方案2：扫码跳转（新增逻辑）
    else if (options.q) {
        try {
            // 1. URL解码
            const decodedUrl = decodeURIComponent(options.q);
            console.log('解码后的URL:', decodedUrl);
            
            // 2. 手动解析URL参数（小程序不支持URL构造函数）
            const queryString = decodedUrl.split('?')[1];
            if (queryString) {
                const params = queryString.split('&');
                for (let param of params) {
                    const [key, value] = param.split('=');
                    if (key === 'stopNo') {
                        stopNoValue = value;
                        break;
                    }
                }
            }
            console.log('扫码跳转模式，解析出的stopNo:', stopNoValue);
        } catch (error) {
            console.error('URL解析失败:', error);
            uni.showToast({
                title: 'URL解析失败',
                icon: 'error'
            });
            return;
        }
    }

    if (stopNoValue) {
        // 解析 stopNo 参数：格式为 "gateNoValue wId warehouseIdValue"
        let splitStr = stopNoValue.split("wId");
        if (splitStr.length === 2) {
            gateNo.value = splitStr[0];
            warehouse.value = splitStr[1];

            console.log('解析成功:', {
                gateNo: gateNo.value,
                warehouse: warehouse.value
            });

            // 开始支付流程
            createParkingOrder();
        } else {
            console.error('stopNo参数格式错误:', stopNoValue);
            uni.showToast({
                title: '参数格式错误',
                icon: 'error'
            });
        }
    } else {
        console.error('缺少stopNo参数');
        uni.showToast({
            title: '缺少必要参数',
            icon: 'error'
        });
    }
});

const createParkingOrder = () => {
    // 显示加载提示
    uni.showLoading({
        title: '正在获取支付信息...',
        mask: true
    });

    uni.login({
        provider: 'weixin',
        success: (res) => {
            getOpenid({ wxCode: res.code }).then(openidRes => {
                openid.value = openidRes.data;

                if (!openid.value) {
                    uni.hideLoading();
                    uni.showToast({
                        title: '获取用户信息失败',
                        icon: 'error'
                    });
                    return;
                }

                // 调用支付接口
                initiatePayment();
            }).catch(error => {
                console.error('获取openid失败:', error);
                uni.hideLoading();
                uni.showToast({
                    title: '获取用户信息失败',
                    icon: 'error'
                });
            });
        },
        fail: (error) => {
            console.error('微信登录失败:', error);
            uni.hideLoading();
            uni.showToast({
                title: '微信登录失败',
                icon: 'error'
            });
        }
    });
};

// 发起支付
const initiatePayment = () => {
    const params = {
        openId: openid.value,
        gateNo: gateNo.value,
        warehouseId: warehouse.value
    };

    console.log('支付参数:', params);

    paymentTemporary(params).then(res => {
        uni.hideLoading();

        if (!res.data) {
            uni.showToast({
                title: '获取支付信息失败',
                icon: 'error'
            });
            return;
        }

        // 发起微信支付
        uni.requestPayment({
            timeStamp: res.data.timeStamp,
            nonceStr: res.data.nonceStr,
            package: res.data.package,
            signType: res.data.signType,
            paySign: res.data.paySign,
            success: function (result) {
                console.log('支付成功:', result);
                uni.showToast({
                    title: '支付成功',
                    icon: 'success',
                    duration: 2000
                    // complete: function () {
                    //     setTimeout(() => {
                    //         uni.navigateBack();
                    //     }, 1000);
                    // }
                });

                // 支付成功回调（如果有的话）
                // if (res.data.tradeId) {
                    // payParkingOrderCallBack({tradeId: res.data.tradeId}).then(item => {
                    //     console.log('停车订单支付成功回调:', item);
                    // });
                // }
            },
            fail: function (err) {
                console.error('支付失败:', err);
                uni.showToast({
                    title: '支付失败',
                    icon: 'error'
                });
            }
        });
    }).catch(error => {
        console.error('获取支付信息失败:', error);
        uni.hideLoading();
        uni.showToast({
            title: '获取支付信息失败',
            icon: 'error'
        });
    });
};


</script>
<style lang="scss" scoped>
.car-stop-container {
    background-color: #f5f5f5;
    height: 100vh;
}

.cell {
    padding: 40rpx 32rpx 0;

    .top-cell {
        margin-bottom: 20rpx;

        .top-cell-title {
            margin-right: 8rpx;

            .title {
                font-size: 40rpx;
                font-weight: bold;
                color: #212121;
                margin-bottom: 8rpx;
            }

            .desc {
                font-size: 28rpx;
                font-weight: 400;
                color: #9e9e9e;
            }
        }

        image {
            width: 284rpx;
            height: 200rpx;
        }
    }
}
</style>
