<template>
    <view class="my-car-container">
        <!-- 车辆列表 -->
        <view class="my-car-list">
            <view class="my-car-item" v-for="car in carList" :key="car.id">
                <view class="my-car-content">
                    <view class="my-car-content-left">
                        <view class="plateNumber">{{ car.plateNo }}</view>
                        <view class="car-info">
                            <text class="carType">{{ car.carType }}</text>
                            <text class="divider">·</text>
                            <text class="energyType">{{ getEnergyTypeLabel(car.energyType) }}</text>
                        </view>
                    </view>
                    <view class="my-car-content-right">
                        <image src="/static/image/carLeft.png" class="car-image" mode="widthFix"></image>
                    </view>
                </view>
                <view class="my-car-item-bottom">
                    <view class="default-section" @tap="setDefaultCar(car)">
                        <radio :checked="car.isDefault" color="#246bfd" class="radio-custom"></radio>
                        <text class="default-text">设置为默认车辆</text>
                    </view>
                    <view class="action-section">
                        <u-icon name="edit-pen" size="30" color="#999" @tap="editCarNav(car.id)"></u-icon>
                        <u-icon name="trash" size="30" color="#999" @tap="deleteCarNav(car.id)"></u-icon>
                    </view>
                </view>
            </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="carList.length === 0">
            <up-empty text="暂无车辆信息" />
        </view>

        <!-- 底部固定的添加车辆按钮 -->
        <view class="bottom-add-btn" @tap="addCarNav">
            <text class="add-icon">+</text>
            <text class="add-text">添加车辆</text>
        </view>
    </view>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import { getCarList, deleteCar, editCar } from '@/api/car';

const carList = ref([]);

onShow(() => {
    getCarList().then(res => {
        console.log(res);
        // 确保数据正确更新
        carList.value = [];
        nextTick(() => {
            carList.value = res.data;
        });
    });
});

// 能源类型选项
const energyTypeOptions = [
    { label: '燃油', value: 1 },
    { label: '纯电', value: 2 },
    { label: '混动', value: 3 }
];

// 能源类型显示映射
const getEnergyTypeLabel = (value) => {
    const option = energyTypeOptions.find(item => item.value === value);
    return option ? option.label : '';
};

// 添加车辆
const addCarNav = () => {
    uni.navigateTo({
        url: '/pages/myCar/myCarAdd?isEdit=false'
    });
};

// 编辑车辆
const editCarNav = (id) => {
    uni.navigateTo({
        url: '/pages/myCar/myCarAdd?isEdit=true&id=' + id
    });
};

// 删除车辆
const deleteCarNav = (id) => {
    console.log(id);
    uni.showModal({
        content: `确定要删除车辆吗？`,
        success: (res) => {
            if (res.confirm) {
                // 显示加载提示
                uni.showLoading({
                    title: '删除中...'
                });

                deleteCar({ id: id }).then(res => {
                    uni.hideLoading();
                    if (res.code === 200) {
                        uni.showToast({
                            title: '删除成功',
                            icon: 'success'
                        });
                        getCarList().then(res => {
                            if (res.data) {
                                // 先清空数组，然后重新赋值，确保触发响应式更新
                                carList.value = [];
                                nextTick(() => {
                                    carList.value = res.data;
                                });
                            }
                        }).catch(err => {
                            console.error('重新获取车辆列表失败:', err);
                        });
                    } else {
                        uni.showToast({
                            title: res.msg || '删除失败',
                            icon: 'none'
                        });
                    }
                }).catch(err => {
                    uni.hideLoading();
                    console.error('删除车辆失败:', err);
                    uni.showToast({
                        title: '删除失败，请重试',
                        icon: 'none'
                    });
                });
            }
        }
    });
};

// 设置默认车辆
const setDefaultCar = (car) => {
    if (car.isDefault) {
        uni.showToast({
            title: '该车已经是默认车辆',
            icon: 'none'
        });
        return;
    }

    // 显示加载提示
    uni.showLoading({
        title: '设置中...'
    });

    editCar({ id: car.id, isDefault: 1 }).then(res => {
        uni.hideLoading();
        if (res.code === 200) {
            uni.showToast({
                title: '设置成功',
                icon: 'success'
            });
            getCarList().then(res => {
                if (res.data) {
                    // 先清空数组，然后重新赋值，确保触发响应式更新
                    carList.value = [];
                    nextTick(() => {
                        carList.value = res.data;
                    });
                }
            }).catch(err => {
                console.error('重新获取车辆列表失败:', err);
            });
        } else {
            uni.showToast({
                title: res.msg || '设置失败',
                icon: 'none'
            });
        }
    }).catch(err => {
        uni.hideLoading();
        console.error('设置默认车辆失败:', err);
        uni.showToast({
            title: '设置失败，请重试',
            icon: 'none'
        });
    });
};
</script>

<style lang="scss" scoped>
.my-car-container {
    background-color: #f5f5f5;
    padding: 25rpx 25rpx 140rpx 25rpx;
    min-height: 100vh;
    position: relative;
}

.my-car-list {
    .my-car-item {
        background: #fff;
        border-radius: 24rpx;
        padding: 32rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

        .my-car-content {
            display: flex;
            align-items: center;

            .my-car-content-left {
                margin-right: 150rpx;

                .plateNumber {
                    font-size: 36rpx;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 12rpx;
                }

                .car-info {
                    display: flex;
                    align-items: center;
                    gap: 8rpx;

                    .carType,
                    .energyType {
                        font-size: 28rpx;
                        color: #666;
                    }

                    .divider {
                        color: #ccc;
                        font-size: 24rpx;
                    }
                }
            }

            .my-car-content-right {
                .car-image {
                    width: 250rpx;
                    height: 140rpx;
                }
            }
        }

        .my-car-item-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 24rpx;
            border-top: 2rpx solid #f0f0f0;

            .default-section {
                display: flex;
                align-items: center;

                .radio-custom {
                    transform: scale(0.9);
                }

                .default-text {
                    font-size: 28rpx;
                    color: #666;
                }
            }

            .action-section {
                display: flex;
                align-items: center;
                gap: 24rpx;
            }
        }
    }
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 0;

    .empty-image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 32rpx;
        opacity: 0.6;
    }

    .empty-text {
        font-size: 32rpx;
        color: #999;
        margin-bottom: 48rpx;
    }
}

/* 底部固定的添加车辆按钮 */
.bottom-add-btn {
    position: fixed;
    bottom: 40rpx;
    width: 85%;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(90deg, #4BA1FC 0%, #9b8eff 100%);
    color: #fff;
    padding: 22rpx;
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;

    .add-icon {
        font-size: 36rpx;
        font-weight: bold;
        margin-right: 12rpx;
    }

    .add-text {
        font-size: 32rpx;
        font-weight: 500;
    }
}
</style>
