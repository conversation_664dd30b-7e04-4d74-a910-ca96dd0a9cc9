<template>
    <view class="warehouse-container">
        <!-- 切换标签 - 固定在顶部 -->
        <view class="tab-header">
            <view class="tab-item" :class="{ active: currentTab === 'parking' }" 
            @tap="switchTab('parking')">停车场库</view>
            <view class="tab-divider"></view>
            <view class="tab-item" :class="{ active: currentTab === 'charging' }" 
            @tap="switchTab('charging')">充电场库
            </view>
        </view>

        <!-- 场库列表内容 - 可滑动区域 -->
        <view class="warehouse-list-container">
            <scroll-view scroll-y class="warehouse-list" @scrolltolower="loadMore">
                <!-- 加载中状态 -->
                <view v-if="isLoading" class="list-content">
                    <view class="loading-container">
                        <up-loading-page loading="true" loading-text="加载中..."></up-loading-page>
                    </view>
                </view>
                
                <!-- 停车场列表 -->
                <view v-else-if="currentTab === 'parking' && parkWareHouseList.length > 0" class="list-content">
                    <view class="warehouse-item" v-for="(item, index) in parkWareHouseList" :key="index" @tap="goToDetail(item.id)">
                        <!-- 整体布局：图片-信息-操作 -->
                        <view class="item-layout">
                            <!-- 左侧：图片 -->
                            <view class="item-image">
                                <image :src="getFirstImage(item.carouselImages)" mode="aspectFill" class="image"></image>
                            </view>
                            
                            <!-- 中间：场库信息 -->
                            <view class="item-info">
                                <text class="name">{{ item.warehouseName }}</text>
                                <text class="time">00:00 - 23:59</text>
                                <text class="address">{{ item.address || '暂无地址信息' }}</text>
                            </view>
                            
                            <!-- 右侧：距离和咨询按钮 -->
                            <view class="item-actions">
                                <view class="distance-info" @tap.stop="goToMap(item)">
                                    <image src="/static/image/map_arrow.png" mode="widthFix" class="distance-icon"></image>
                                    <text class="distance-text">{{ item.distance ? `${item.distance}km` : '未知' }}</text>
                                </view>
                                <view class="consult-btn" @tap.stop="makePhoneCall(item)" v-if="item.managerPhone">
                                    <up-icon name="phone" size="20" color="#ffffff"></up-icon>
                                    <text class="consult-text">咨询</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view v-else-if="currentTab === 'parking' && parkWareHouseList.length === 0" class="list-content">
                    <up-empty text="暂无停车场数据" mode="data"></up-empty>
                </view>

                <!-- 充电场列表 -->
                <view v-else class="list-content">
                    <up-empty text="暂无充电场数据" mode="data"></up-empty>
                </view>
            </scroll-view>
        </view>

        <custom-tab-bar></custom-tab-bar>
    </view>
</template>

<script setup>
import CustomTabBar from "@/components/custom-tab-bar/index.vue";
import { ref } from 'vue';
import { getParkWareHouseList } from '@/api/warehouse';
import { onLoad } from '@dcloudio/uni-app';

// 当前选中的标签
const currentTab = ref('parking');
const parkWareHouseList = ref([]);
const userLocation = ref(null);
const isLoading = ref(true);

onLoad(() => {
    isLoading.value = true;
    getUserLocation();
});

// 获取用户位置
const getUserLocation = () => {
    uni.getLocation({
        type: 'gcj02',
        success: (res) => {
            userLocation.value = {
                latitude: res.latitude,
                longitude: res.longitude
            };
            initParkWarehouse();
        },
        fail: (err) => {
            console.error('获取位置失败:', err);
            // 获取位置失败时也加载数据，但不显示距离
            initParkWarehouse();
        }
    });
};

const initParkWarehouse = () => {
    getParkWareHouseList().then(res => {
        const list = res.data;
        // 计算每个场库的距离
        if (userLocation.value) {
            list.forEach(item => {
                item.distance = calculateDistance(
                    userLocation.value.latitude,
                    userLocation.value.longitude,
                    item.latitude,
                    item.longitude
                );
            });
            
            // 按照距离从近到远排序
            list.sort((a, b) => {
                // 如果没有距离信息的放在最后
                if (a.distance === undefined && b.distance === undefined) return 0;
                if (a.distance === undefined) return 1;
                if (b.distance === undefined) return -1;
                return a.distance - b.distance;
            });
        }
        parkWareHouseList.value = list;
        isLoading.value = false;
    }).catch(err => {
        console.error('加载场库列表失败:', err);
        isLoading.value = false;
    });
};

// 计算两点之间的距离（km）
const calculateDistance = (lat1, lng1, lat2, lng2) => {
    const radLat1 = lat1 * Math.PI / 180.0;
    const radLat2 = lat2 * Math.PI / 180.0;
    const a = radLat1 - radLat2;
    const b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
    let s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
    s = s * 6378.137; // 地球半径
    s = Math.round(s * 100) / 100; // 保留两位小数
    return s;
};

// 获取第一张图片
const getFirstImage = (carouselImages) => {
    if (!carouselImages) return '/static/image/parkPay.png'; // 默认图片

    try {
        // 处理字符串格式的图片数据
        if (typeof carouselImages === 'string') {
            // 尝试直接解析为数组
            const parsedArray = JSON.parse(carouselImages);
            if (Array.isArray(parsedArray) && parsedArray.length > 0) {
                // 如果数组第一个元素是字符串（URL），直接返回
                if (typeof parsedArray[0] === 'string') {
                    return parsedArray[0];
                }
                // 如果数组第一个元素还是数组，再解析一层
                if (Array.isArray(parsedArray[0]) && parsedArray[0].length > 0) {
                    return parsedArray[0][0];
                }
            }
        }
        // 如果是数组格式
        if (Array.isArray(carouselImages) && carouselImages.length > 0) {
            return carouselImages[0];
        }
    } catch (error) {
        console.error('解析图片数据失败:', error);
        // 如果JSON解析失败，检查是否是直接的URL字符串
        if (typeof carouselImages === 'string' && carouselImages.startsWith('http')) {
            return carouselImages;
        }
    }

    return '/static/image/parkPay.png'; // 默认图片
};

// 切换标签
const switchTab = (tab) => {
    currentTab.value = tab;
    if (tab === 'parking') {
        isLoading.value = true;
        initParkWarehouse();
    }
};

const goToMap = (item) => {
    uni.openLocation({
        latitude: item.latitude,
        longitude: item.longitude,
        name: item.warehouseName,
        address: item.address,
        success: () => {
            console.log('打开地图成功');
        }
    });
};

// 拨打电话咨询
const makePhoneCall = (item) => {
    if (!item.managerPhone) {
        uni.showToast({
            title: '暂无联系电话',
            icon: 'none'
        });
        return;
    }
    
    uni.showModal({
        title: '拨打电话',
        content: `是否拨打 ${item.managerPhone} 的咨询电话？`,
        success: (res) => {
            if (res.confirm) {
                uni.makePhoneCall({
                    phoneNumber: item.managerPhone,
                    success: () => {
                        console.log('拨打电话成功');
                    },
                    fail: (err) => {
                        console.error('拨打电话失败:', err);
                        uni.showToast({
                            title: '拨打电话失败',
                            icon: 'none'
                        });
                    }
                });
            }
        }
    });
};

// 加载更多
const loadMore = () => {
    console.log('加载更多数据');
};
// 跳转详情
const goToDetail = (id)=>{
    uni.navigateTo({
        url: `/pages/warehouse/warehouseDetail?id=${id}`
    })
}
</script>

<style lang="scss" scoped>
.warehouse-container {
    height: 100vh;
    background-color: #f5f5f5;
    position: relative;
}

.tab-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #4BA1FC, #7e6dff);
    padding: 10rpx 30rpx;
    z-index: 1000;
    flex-shrink: 0;

    .tab-item {
        flex: 1;
        text-align: center;
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.9);
        padding: 20rpx 0;
        position: relative;

        &.active {
            color: #ffffff;
            font-weight: bold;

            &::after {
                content: '';
                position: absolute;
                bottom: 8rpx;
                left: 50%;
                transform: translateX(-50%);
                width: 40rpx;
                height: 4rpx;
                background-color: #ffffff;
                border-radius: 2rpx;
            }
        }
    }
}

.tab-divider {
    width: 2rpx;
    height: 40rpx;
    background-color: rgba(255, 255, 255, 0.3);
    margin: 0 20rpx;
}

.warehouse-list-container {
    flex: 1;
    overflow: hidden;
    margin-top: 100rpx; // 为固定的tab-header留出空间
}

.warehouse-list {
    height: 100%;
}

.list-content {
    padding: 20rpx;
    padding-bottom: 190rpx;
}

.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400rpx;
    padding: 100rpx 0;
}

.warehouse-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);

    // 整体布局：图片-信息-操作
    .item-layout {
        display: flex;
        align-items: flex-start;
        gap: 20rpx;

        // 左侧：图片
        .item-image {
            width: 200rpx;
            height: 160rpx;
            border-radius: 12rpx;
            overflow: hidden;
            flex-shrink: 0;
            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

            .image {
                width: 100%;
                height: 100%;
            }
        }

        // 中间：场库信息
        .item-info {
            flex: 1;
            display: flex;
            flex-direction: column;

            .name {
                font-size: 30rpx;
                font-weight: 600;
                color: #333;
                margin-bottom: 8rpx;
            }

            .time {
                font-size: 26rpx;
                color: #666;
                margin: 22rpx 0;
            }

            .address {
                font-size: 24rpx;
                color: #333;
                word-break: break-all;
            }
        }

        // 右侧：距离和咨询按钮
        .item-actions {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12rpx;
            flex-shrink: 0;

            .distance-info {
                margin-bottom: 30rpx;
                display: flex;
                flex-direction: column;
                align-items: center;

                .distance-icon {
                    width: 40rpx;
                    height: 40rpx;
                    margin-bottom: 4rpx;
                }

                .distance-text {
                    font-size: 20rpx;
                    color: #4BA1FC;
                    font-weight: 500;
                }
            }

            .consult-btn {
                display: flex;
                align-items: center;
   
                background: linear-gradient(135deg, #4BA1FC, #7e6dff);
                border-radius: 20rpx;
                padding: 8rpx 16rpx;

                .consult-text {
                    color: #ffffff;
                    font-size: 22rpx;
                    font-weight: 500;
                    margin-left: 6rpx;
                }
            }
        }
    }
}

.empty-tip {
    text-align: center;
    color: #999;
    font-size: 28rpx;
    padding: 100rpx 0;
}
</style>
