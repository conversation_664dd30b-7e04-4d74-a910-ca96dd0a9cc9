<template>
    <view class="parking-vip-package">
        <!-- VIP套餐卡片列表 -->
        <view class="vip-package-list">
            <!-- 渲染3个VIP卡片 -->
            <view class="vip-card" v-for="(cardData, index) in vipCards" :key="index">
                <!-- 上半部分：内容和图片 -->
                <view class="content-section">
                    <view class="vip-info">
                        <view class="vip-plate">
                            车牌号：{{ cardData.plateNo || '--' }}
                        </view>
                        <view class="vip-title-container">
                            场库：{{ cardData.warehouseName || '--' }}
                        </view>
                        <view class="vip-expire">
                            开始：{{ cardData.beginVipTime || '--' }}
                        </view>
                        <view class="vip-expire">
                            结束：{{ cardData.endVipTime || '--' }}
                        </view>
                    </view>
                    <view class="right-section">
                        <view class="car-image" v-if="cardData.plateNo">
                            <image src="/static/image/carLeft.png" mode="aspectFit"></image>
                        </view>
                    </view>
                </view>
                <!-- 按钮区域：独占最后一行 -->
                <view class="button-section">
                    <view class="vip-link" 
                          :class="{ disabled: !isButtonClickable(cardData, index) }"
                          @tap="handleAction(cardData, index)" >
                        {{ getButtonText(cardData, index) }}
                    </view>
                </view>
            </view>
        </view>
        
        <!-- 注意事项 -->
        <!-- <view class="notice-section">
            <view class="notice-title">注意事项</view>
            <view class="notice-content">
                <text class="notice-text" v-if="props.userType === 'VIP套餐'">
                    最多可购买三辆车，每辆车价格1800元/年，手机号和车牌号一一对应，如需修改手机号或者车牌号，请联系客服。
                </text>
                <text class="notice-text" v-else>
                    最多可购买三辆车，第一辆车免费，第二辆车600元/年，第三辆车1800元/年，手机号和车牌号一一对应，如需修改手机号或者车牌号，请联系客服。
                </text>
            </view>
        </view> -->
    </view>
</template>

<script setup>
import { ref, computed } from "vue";
import { getVipUserPackageList } from "@/api/package";

// 定义props
const props = defineProps({
    userType: {
        type: String,
        default: 'VIP套餐' // 默认VIP套餐
    }
});

// 响应式数据
const vipUserCarList = ref(null);

// 计算属性
const vipCards = computed(() => {
    const carPackages = vipUserCarList.value || [];
    const cards = [];

    // 添加已有数据的卡片
    for (let i = 0; i < carPackages.length && i < 3; i++) {
        const car = carPackages[i];
        cards.push({
            ...car,
            warehouseName: car.warehouseName,
            // 判断是否未过期：beginVipTime和endVipTime都为null表示未过期
            isVip: car.beginVipTime !== null && car.endVipTime !== null
        });
    }

    // 填充空位到3个
    while (cards.length < 3) {
        cards.push({
            plateNo: null,
            warehouseName: null,
            beginVipTime: null,
            endVipTime: null,
            isVip: false
        });
    }
    console.log('cards', cards)
    return cards;
});

// 计算当前有车辆数量
const currentCarCount = computed(() => {
    const carPackages = vipUserCarList.value || [];
    return carPackages.length;
});

// 计算vipType
const vipType = computed(() => {
    return props.userType === '集团套餐' ? 1 : 2;
});

// 初始化数据
const initData = async () => {
    try {
        uni.showLoading({ title: "加载中..." });
        await loadVipUserCarList();
        uni.hideLoading();
    } catch (error) {
        uni.hideLoading();
        uni.showToast({
            title: error.message || "数据加载失败",
            icon: "none",
        });
    }
};

// 加载VIP用户数据
const loadVipUserCarList = async () => {
    try {
        const res = await getVipUserPackageList(vipType.value);
        vipUserCarList.value = res.data;
    } catch (error) {
        throw new Error(error.message || "获取VIP用户数据失败");
    }
};

// 判断按钮是否可点击
const isButtonClickable = (cardData, index) => {
    if (cardData.plateNo !== null) {
        return true;
    }
    return index <= currentCarCount.value;
};

// 获取按钮文字
const getButtonText = (cardData, index) => {
    if (cardData.plateNo === null) {
        return '购买';
    }
    if (cardData.isVip) {
        return '续费';
    } else {
        return '购买';
    }
};

// 处理按钮点击
const handleAction = (cardData, index) => {
    if (!isButtonClickable(cardData, index)) {
        return;
    }

    // 有数据的卡片，续费或重新购买
    const isRenewal = cardData.isVip;
    const packageOrder = {
        warehouseId: cardData.warehouseId,
        warehouseName: cardData.warehouseName,
        plateNo: cardData.plateNo,
        index: index + 1,   //作为索引，判断是第几辆车
        vipType: vipType.value,
        isRenewal: isRenewal,
        beginVipTime: cardData.beginVipTime,
        expirationTime: cardData.endVipTime,
    };

    // 根据用户类型设置不同的套餐信息
    if (vipType.value === 2) {
        // VIP客户：固定套餐信息
        packageOrder.packageName = "VIP年度套餐";
        packageOrder.packagePrice = 0.01;
        packageOrder.packageDays = 1;
        packageOrder.packageId = 6;
    } else {
        // 集团客户：套餐信息为空，需要在购买页面选择
        packageOrder.packageName = null;
        packageOrder.packagePrice = null;
        packageOrder.packageDays = null;
        packageOrder.packageId = null;
    }

    console.log('packageOrder', packageOrder)

    // 跳转到购买页面
    uni.navigateTo({
        url: `/pages/package/packageVipBuy?packageOrder=${encodeURIComponent(
            JSON.stringify(packageOrder)
        )}`,
    });
};

// 暴露方法给父组件调用
defineExpose({
    initData
});
</script>

<style lang="scss" scoped>
.parking-vip-package {
    padding: 0 20rpx;
    min-height: 100vh;
}

.vip-package-list {
    .vip-card {
        display: flex;
        flex-direction: column;
        border-radius: 16rpx;
        overflow: hidden;
        background-image: url('https: //test-parknew.lgfw24hours.com:3443/statics/wx/specialVipHeader.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding-bottom: 40rpx;

        .content-section {
            color: #fff;
            display: flex;
            align-items: center;
            padding: 20rpx 20rpx 0 20rpx;

            .vip-info {
                padding: 10rpx 30rpx 0;
                display: flex;
                flex-direction: column;
                justify-content: center;

                .vip-title-container {
                    margin: 5rpx 0;
                    font-size: 32rpx;
                }

                .vip-plate {
                    font-size: 36rpx;
                    font-weight: bold;
                    margin: 10rpx 0;
                }

                .vip-expire {
                    font-size: 24rpx;
                }
            }

            .right-section {
                margin-left: 40rpx;
                display: flex;
                align-items: center;
                justify-content: center;

                .car-image {
                    width: 180rpx;
                    height: 180rpx;

                    image {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }

        .button-section {
            display: flex;
            justify-content: flex-end;
            margin-right: 40rpx;

            .vip-link {
                background: #007aff;
                padding: 5rpx 20rpx;
                border-radius: 40rpx;
                text-align: center;
                font-size: 28rpx;
                font-weight: bold;
                color: #fff;

                &.disabled {
                    background: #ccc;
                    color: #999;
                }
            }
        }
    }
}

.notice-section {
    margin-top: 30rpx;
    padding: 20rpx;
    background: #fff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
    
    .notice-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 16rpx;
        padding-bottom: 12rpx;
        border-bottom: 1rpx solid #f0f0f0;
    }
    
    .notice-content {
        .notice-text {
            font-size: 26rpx;
            color: #666;
            line-height: 1.6;
            display: block;
        }
    }
}
</style>
