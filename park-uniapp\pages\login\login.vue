<template>
	<view class="login-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: menuButtonTop + 'px', height: menuButtonHeight + 'px' }">
			<view class="nav-back" @tap="handleBack">
				<up-icon name="arrow-left" size="20" color="#fff"></up-icon>
			</view>
		</view>

		<!--登录头部图片-->
		<view class="login-header">
			<image src="/static/image/login.png" mode="aspectFill" class="login-header-image" />
		</view>

		<!--登录内容区域-->
		<view class="login-content">
			<!--登录表单-->
			<view class="login-form">
				<view class="login-form-phone">
					<text class="login-form-text">手机号</text>
					<text class="login-form-text">|</text>
					<input type="text" placeholder="请输入手机号" class="login-form-input" maxlength="11"
						v-model="loginForm.phoneNumber" />
				</view>
				<view class="login-form-code">
					<text class="login-form-text">验证码</text>
					<text class="login-form-text">|</text>
					<input type="text" placeholder="请输入验证码" class="login-form-input" maxlength="6"
						v-model="loginForm.msgCode" />
					<button class="login-form-code-button" @tap="fetchCode" :disabled="buttonDisabled">{{
						buttonText }}</button>
				</view>
			</view>

			<!--登录按钮-->
			<view class="login-button" @tap="fetchLogin">
				<text class="login-button-text">登录</text>
			</view>

			<!-- 用户协议 -->
			<view class="agreement">
				<view class="checkbox" :class="{ checked: isAgree }" @tap="toggleAgreement">
					<up-icon v-if="isAgree" name="checkmark" size="22" color="#fff"></up-icon>
				</view>
				<text class="agreement-text">我已阅读并同意</text>
				<text class="agreement-link" @tap="showAgreement">《用户协议》</text>
				<text class="agreement-text">和</text>
				<text class="agreement-link" @tap="showPrivacy">《隐私政策》</text>
			</view>

			<!-- 微信登录按钮 -->
			<button class="wechat-login" v-if="!isAgree" @click="checkBeforeLogin">
				<view class="wechat-icon">
					<up-icon name="weixin-fill" size="32" color="#fff"></up-icon>
				</view>
				<!-- <text class="wechat-text">手机号快捷登录</text> -->
			</button>
			<button class="wechat-login" v-else open-type="getPhoneNumber" @getphonenumber="handleWechatLogin">
				<view class="wechat-icon">
					<up-icon name="weixin-fill" size="32" color="#fff"></up-icon>
				</view>
				<!-- <text class="wechat-text">手机号快捷登录</text> -->
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, onUnmounted, onMounted } from 'vue'
import { setStorageSync } from '@/utils/utils'
import { getCode, login, loginWx } from '@/api/login'

// 系统信息相关
const menuButtonHeight = ref(0);
const menuButtonTop = ref(0);

// 初始化系统信息
const initSystemInfo = () => {
	try {
		const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
		menuButtonHeight.value = menuButtonInfo.height;
		menuButtonTop.value = menuButtonInfo.top;
	} catch (error) {
		console.error('获取系统信息失败:', error);
	}
};

// 回退处理
const handleBack = () => {
	uni.navigateBack({
		delta: 1,
		fail: () => {
			uni.switchTab({
				url: '/pages/home/<USER>'
			});
		}
	});
};

// 登录表单
const loginForm = ref({
	phoneNumber: '',
	msgCode: '',
	wxCode: ''
})

// 获取验证码
const buttonText = ref('获取验证码')
const buttonDisabled = ref(false)
let countdownInterval = null;
const fetchCode = async () => {
	if (loginForm.value.phoneNumber === '' || loginForm.value.phoneNumber.length !== 11) {
		uni.showToast({
			title: '请输入正确的手机号',
			icon: 'none'
		})
		return
	}
	buttonDisabled.value = true
	let timeLeft = 60;
	buttonText.value = ` ${timeLeft} s`
	countdownInterval = setInterval(() => {
		timeLeft--;
		if (timeLeft > 0) {
			buttonText.value = ` ${timeLeft} s`
		} else {
			clearInterval(countdownInterval)
			buttonText.value = '获取验证码'
			buttonDisabled.value = false
		}
	}, 1000);
	await getCode({
		phoneNumber: loginForm.value.phoneNumber
	})
	uni.showToast({
		title: '验证码已发送',
		icon: 'success'
	})
}

// 用户协议勾选状态
const isAgree = ref(false)

// 切换协议勾选状态
const toggleAgreement = () => {
	isAgree.value = !isAgree.value
}

// 查看用户协议
const showAgreement = () => {
	uni.navigateTo({
		url: '/pages/aggrement/user-aggrement'
	})
}

// 查看隐私政策
const showPrivacy = () => {
	uni.navigateTo({
		url: '/pages/aggrement/privacy-aggrement'
	})
}
// 检查是否勾选协议
const checkBeforeLogin = () => {
	if (!isAgree.value) {
		uni.showToast({
			title: '请阅读并同意用户协议和隐私政策',
			icon: 'none'
		})
		return false
	}
	return true
}
// -------------------- 手机号验证码登录处理 --------------------
const fetchLogin = async () => {
	if (loginForm.value.phoneNumber === '' || loginForm.value.phoneNumber.length !== 11
		|| loginForm.value.msgCode === '' || loginForm.value.msgCode.length !== 6) {
		uni.showToast({
			title: '请输入正确的手机号或者验证码',
			icon: 'none'
		})
		return
	}

	if (!checkBeforeLogin()) return

	const loginRes = await getWxCode()
	loginForm.value.wxCode = loginRes.code
	if (!loginForm.value.wxCode) {
		uni.showToast({
			title: '获取微信code失败',
			icon: 'none'
		})
		return
	}

	uni.showLoading({
		title: '登录中...',
		mask: true
	});
	try {
		const res = await login(loginForm.value)
		setStorageSync('token', res.data.token)
		setStorageSync('wxUser', res.data.wxUser)
		uni.hideLoading();
		uni.showToast({
			title: '登录成功',
			icon: 'success',
			duration: 1500
		});
		setTimeout(() => {
			uni.switchTab({
				url: '/pages/home/<USER>'
			});
		}, 1500);
	} catch (error) {
		uni.showToast({
			title: error.msg,
			icon: 'none'
		})
	}
}

// -------------------- 微信登录处理 --------------------
const handleWechatLogin = async (e) => {
	checkBeforeLogin();
	if (e.detail.errMsg === 'getPhoneNumber:ok') {
		const phoneCode = e.detail.code;
		uni.login({
			provider: 'weixin',
			success: (res) => {
				const code = res.code;
				const params = {
					wxCode: code,
					phoneCode: phoneCode
				}
				uni.showLoading({
					title: '登录中...',
					mask: true
				});
				loginWx(params).then(res => {
					// 存储登录信息
					setStorageSync('token', res.data.token)
					setStorageSync('wxUser', res.data.wxUser)
					uni.hideLoading();
					uni.showToast({
						title: '登录成功',
						icon: 'success',
						duration: 1500
					});
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/home/<USER>'
						});
					}, 1500);
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: '登录失败，请重试',
						icon: 'none'
					});
				});
			},
			fail: (err) => {
				uni.showToast({
					title: '获取微信授权失败',
					icon: 'none'
				});
			}
		})
	} else {
		uni.showToast({
			title: '获取手机号失败',
			icon: 'none'
		});
	}
}

// 获取微信临时code
const getWxCode = () => {
	return new Promise((resolve, reject) => {
		uni.login({
			provider: 'weixin',
			success: resolve,
			fail: reject
		})
	})
}

// 组件卸载时清除定时器
onUnmounted(() => {
	if (countdownInterval) {
		clearInterval(countdownInterval)
	}
})

// 生命周期
onMounted(() => {
	initSystemInfo();
});

</script>

<style lang="scss" scoped>
.login-container {
	display: flex;
	flex-direction: column;
	min-height: 100vh;
	position: relative;
	background: #f5f9ff;
	overflow: hidden;
}

// 自定义导航栏
.custom-navbar {
	position: fixed;
	left: 0;
	right: 0;
	z-index: 999;
	display: flex;
	align-items: center;
	padding-left: 15rpx;

	.nav-back {
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: rgba(0, 0, 0, 0.2);
		border-radius: 50%;
		backdrop-filter: blur(10px);
		-webkit-backdrop-filter: blur(10px);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
			background: rgba(0, 0, 0, 0.3);
		}
	}
}

// 登录头部图片
.login-header {
	width: 100%;
	height: 40vh;
	position: relative;
	z-index: 1;

	.login-header-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

// 登录内容区域
.login-content {
	position: relative;
	margin-top: -60rpx;
	background: #ffffff;
	border-radius: 40rpx 40rpx 0 0;
	padding: 60rpx 40rpx 40rpx;
	z-index: 2;
	flex: 1;
	box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
}

// 登录表单
.login-form {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	z-index: 1;

	.login-form-phone,
	.login-form-code {
		width: 90%;
		background: rgba(255, 255, 255, 0.95);
		border-radius: 16rpx;
		padding: 32rpx;
		display: flex;
		align-items: center;
		margin-bottom: 24rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.03);
		border: 1rpx solid rgba(0, 0, 0, 0.08);

		.login-form-text {
			color: #333333;
			margin-right: 20rpx;
			font-size: 28rpx;
			font-weight: 500;
		}

		.login-form-input {
			flex: 1;
			font-size: 28rpx;
			color: #333333;

			&::placeholder {
				color: #999999;
			}
		}
	}

	.login-form-code {
		.login-form-code-button {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 160rpx;
			height: 56rpx;
			border-radius: 32rpx;
			font-size: 24rpx;
			color: #fff;
			background: linear-gradient(135deg, #60adff, #8677fc);
			box-shadow: 0 4rpx 12rpx rgba(96, 173, 255, 0.2);
			transition: all 0.3s ease;
			font-weight: 500;
			padding: 0;

			&:active {
				transform: scale(0.98);
				box-shadow: 0 2rpx 8rpx rgba(96, 173, 255, 0.15);
			}

			&[disabled] {
				opacity: 0.7;
				background: #cccccc;
			}
		}
	}
}

// 用户协议
.agreement {
	display: flex;
	align-items: center;
	margin: 30rpx 40rpx;
	z-index: 1;

	.checkbox {
		width: 36rpx;
		height: 36rpx;
		border: 2rpx solid #ddd;
		border-radius: 50%;
		margin-right: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;

		&.checked {
			background: #60adff;
			border-color: #60adff;
		}
	}

	.agreement-text {
		font-size: 24rpx;
		color: #666666;
	}

	.agreement-link {
		font-size: 24rpx;
		color: #60adff;
		margin: 0 4rpx;

		&:active {
			opacity: 0.8;
		}
	}
}

// 登录按钮
.login-button {
	width: 100%;
	margin-top: 40rpx;
	padding: 32rpx 0;
	background: linear-gradient(135deg, #60adff, #8677fc);
	border-radius: 16rpx;
	text-align: center;
	box-shadow: 0 12rpx 24rpx rgba(96, 173, 255, 0.25);
	transition: all 0.3s ease;
	z-index: 1;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
		transform: translateX(-100%);
	}

	&:active {
		transform: scale(0.98);
		box-shadow: 0 6rpx 12rpx rgba(96, 173, 255, 0.2);

		&::before {
			transform: translateX(100%);
			transition: transform 0.6s ease;
		}
	}

	.login-button-text {
		color: #fff;
		font-size: 32rpx;
		font-weight: 600;
		letter-spacing: 3rpx;
	}
}

// 微信登录
.wechat-login {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 60rpx;
	z-index: 1;
	background: none;
	border: none;
	padding: 0;
	line-height: 1;

	&::after {
		border: none;
	}

	.wechat-icon {
		width: 88rpx;
		height: 88rpx;
		background-color: #40c27f;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 8rpx 16rpx rgba(64, 194, 127, 0.2);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
			box-shadow: 0 4rpx 8rpx rgba(64, 194, 127, 0.15);
		}
	}
}

// 自定义弹窗样式
.custom-popup {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;

	// 遮罩层
	.popup-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.6);
	}

	// 弹窗内容
	.popup-content {
		position: absolute;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: #ffffff;
		border-radius: 24rpx 24rpx 0 0;
		padding: 40rpx 30rpx;
		transform: translateY(0);

		.popup-header {
			text-align: center;
			margin-bottom: 40rpx;

			.popup-title {
				font-size: 34rpx;
				font-weight: 600;
				color: #333;
				margin-bottom: 20rpx;
				display: block;
			}

			.popup-desc {
				font-size: 28rpx;
				color: #666;
				line-height: 1.5;
			}
		}

		.popup-body {
			padding: 0 30rpx;

			.phone-number {
				background: #ffffff;
				padding: 24rpx;
				border-radius: 12rpx;
				text-align: center;
				margin-bottom: 24rpx;
				font-size: 36rpx;
				color: #333;
				font-weight: 500;
				position: relative;

				.phone-tip {
					font-size: 24rpx;
					color: #666;
					margin-top: 8rpx;
					display: block;
				}

				&:active {
					opacity: 0.8;
				}
			}

			.cancel-btn {
				text-align: center;
				font-size: 32rpx;
				color: #666;
				padding: 20rpx 0;

				&:active {
					opacity: 0.6;
				}
			}
		}
	}
}
</style>
