/*
 *  添加路由拦截器，不在白名单内的页面须登录才能访问
 */
//白名单 不需要验证token
const whiteList = [
  '/pages/home/<USER>',
  '/pages/warehouse/warehouse',
  '/pages/warehouse/warehouseDetail',
	'/pages/login/login',
  '/pages/mine/mine',
  '/pages/noPlate/noPlateIn',
  '/pages/noPlate/noPlateOut',
  '/pages/aggrement/user-aggrement',
  '/pages/aggrement/privacy-aggrement',
  '/pages/carStop/carStop'
]
//登录页
export default function initPermission() {
  /**
   * 页面跳转拦截器
   */
  const list = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']
  list.forEach(item => {
    //用遍历的方式分别为,uni.navigateTo,uni.redirectTo,uni.reLaunch,uni.switchTab这4个路由方法添加拦截器
    uni.addInterceptor(item, {
      invoke(e) {
        // 调用前拦截
        const userInfo = uni.getStorageSync('wxUser')
        //获取用户的token
        const token = uni.getStorageSync('token')
        //获取要跳转的页面路径（url去掉"?"和"?"后的参数）
        const url = e.url.split('?')[0]
        let notNeed = whiteList.includes(url)
        // 如果在whiteList里面就不需要登录
        if (notNeed) {
          return e
        } else {
          //需要登录
          if (!token) {
            // 判断用户是否要登录
            uni.showModal({
              title: '温馨提示',
              content: '您还未登录，请先登录',
              success: function (res) {
                if (res.confirm) {
                  uni.reLaunch({
                    url: '/pages/login/login'
                  })
                }
              }
            })
            return false
          } else {
            return e
          }
        }
      },
      fail(err) {
        // 失败回调拦截
        console.log(err)
      }
    })
  })
}
