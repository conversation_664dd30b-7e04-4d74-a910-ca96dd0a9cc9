<template>
  <view>
    <!-- 场库选择弹出框 -->
    <up-popup :show="show" mode="bottom" round="20" :safeAreaInsetBottom="true" @close="handleClose">
      <view class="warehouse-popup">
        <view class="popup-header">
          <text class="popup-title">选择场库</text>
          <up-icon name="close" size="18" color="#999" @click="handleClose"></up-icon>
        </view>
        <scroll-view class="warehouse-list" scroll-y :style="{ maxHeight: windowHeightHalf + 'px' }">
          <view v-for="warehouse in warehouseList" :key="warehouse.id" class="warehouse-item"
            :class="{ active: warehouse.id === currentWarehouse.id }" @click="handleSelect(warehouse)">
            <text class="warehouse-item-name">{{ warehouse.name }}</text>
            <up-icon v-if="warehouse.id === currentWarehouse.id" name="checkmark" size="16" color="#40a9ff"></up-icon>
          </view>
        </scroll-view>
      </view>
    </up-popup>
  </view>
</template>

<script setup>

// 定义props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  warehouseList: {
    type: Array,
    default: () => []
  },
  currentWarehouse: {
    type: Object,
    default: () => ({})
  },
  windowHeightHalf: {
    type: Number,
    default: 400
  }
});

// 定义emits
const emit = defineEmits(['close', 'select']);

// 关闭弹出框
const handleClose = () => {
  emit('close');
};

// 选择场库
const handleSelect = (warehouse) => {
  emit('select', warehouse);
};
</script>

<style lang="scss" scoped>
// 场库选择弹出框
.warehouse-popup {
  background-color: #fff;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx;
    border-bottom: 1rpx solid #e2e2e2;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .warehouse-list {
    padding: 16rpx 0;

    .warehouse-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 32rpx;
      border-bottom: 1rpx solid #f8f9fa;

      &:last-child {
        border-bottom: none;
      }

      &.active {
        background-color: #f0f8ff;

        .warehouse-item-name {
          color: #2da0fe;
          font-weight: 500;
        }
      }

      &:active {
        background-color: #f5f5f5;
      }

      .warehouse-item-name {
        font-size: 30rpx;
        color: #333;
        text-align: center;
        flex: 1;
      }
    }
  }
}
</style> 