<template>
    <view class="charging-package-component">
        <view class="empty-state">
            <up-icon name="file-text" size="100" color="#d0d0d0"></up-icon>
            <text class="empty-text">暂无充电套餐</text>
        </view>
    </view>
</template>

<script setup>

// 初始化数据方法
const initData = async () => {
    console.log('充电套餐组件初始化 - 暂无数据');
};

// 暴露方法给父组件调用
defineExpose({
    initData
});

</script>

<style lang="scss" scoped>
.charging-package-component {
    padding: 20rpx;
    min-height: 80vh;
    
    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60rpx 40rpx;
        
        .empty-text {
            font-size: 32rpx;
            color: #5f5f5f;
            font-weight: 500;
        }
    }
}
</style>