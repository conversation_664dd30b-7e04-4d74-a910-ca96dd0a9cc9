<template>
    <view class="vip-page">
        <image class="vip-bg" src="https://test-parknew.lgfw24hours.com:3443/statics/wx/vipHeader.png" 
        mode="widthFix">
        </image>
        <!-- 顶部卡片 -->
        <view class="vip-card">
            <view class="vip-info">
                <!-- 场库选择 -->
                <view class="vip-title-container" @tap="showWarehouseSelector">
                    <text class="vip-title">{{ currentWarehouse.name }}</text>
                    <up-icon name="arrow-right" size="12" color="#fff"></up-icon>
                </view>
                <!-- 车辆选择 -->
                <view class="vip-plate" @tap="openCarSelector">
                    <text>车牌号：</text>
                    <text class="plate-number">{{ selectedCar.plateNo || '点击选择' }}</text>
                    <up-icon name="arrow-right" size="12" color="#fff"></up-icon>
                </view>
                <!-- 开始结束时间 -->
                <view class="vip-expire">开始：{{ userPackagePlate?.beginVipTime || '--' }}</view>
                <view class="vip-expire">结束：{{ userPackagePlate?.endVipTime || '--' }}</view>
                <view class="vip-link" @tap="goToRecord">查看续费与开通记录</view>
            </view>
        </view>

        <!-- 套餐选择 -->
        <view class="package-list">
            <view class="package-title">选择普通充值套餐</view>
            <view class="package-grid" v-if="packageList?.length > 0">
                <view v-for="item in packageList" :key="item.id" class="package-item"
                    :class="{ active: choosePackage.id === item.id }" @click="handleChoosePackage(item)">
                    <view class="package-content">
                        <view class="package-name">{{ item.packageName }}</view>
                        <view class="package-price">
                            <text class="price-symbol">￥</text>
                            <text class="price-amount">{{ item.packagePrice }}</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 空状态显示 -->
            <view class="empty-state" v-else>
                <up-empty mode="data" text="暂无可用套餐" />
            </view>
            <button class="buy-btn" :disabled="!choosePackage.id || packageList.length === 0" @click="handleBuyPackage">
                {{ userPackagePlate?.endVipTime ? '续费套餐' : '购买套餐' }}
            </button>
        </view>

        <!-- 场库选择器组件 -->
        <warehouse-selector :show="wareHouseSelector" :warehouse-list="wareHouseList"
            :current-warehouse="currentWarehouse" :window-height-half="400" @close="closeWarehouseSelector"
            @select="selectWarehouse" />

        <!-- 车辆选择器 -->
        <u-picker :show="showCarSelector" :columns="[carList]" @confirm="onCarConfirm" @cancel="showCarSelector = false"
            keyName="plateNo"></u-picker>
    </view>
</template>

<script setup>
import { ref } from 'vue';
import { getPackageList, getUserPackagePlate} from '@/api/package';
import { getParkWareHouseList } from '@/api/warehouse';
import WarehouseSelector from '@/components/warehouse-selector/warehouse-selector.vue';
import { getCarList } from '@/api/car';
// 普通套餐类型标识
const vipType = ref(0)

// 场库选择器
const wareHouseList = ref([]);
const wareHouseSelector = ref(false);
const currentWarehouse = ref({ id: 0, name: "选择场库"});

// 套餐列表
const packageList = ref([]);
const choosePackage = ref({});

// 车辆列表
const carList = ref([]);
const selectedCar = ref({});
const showCarSelector = ref(false);
const userPackagePlate = ref({});

// 统一初始化数据
const initData = async () => {
    await initWarehouseData();
    await initPackageData();
    await initCarData();
};

// 初始化场库数据
const initWarehouseData = async () => {
    const res = await getParkWareHouseList();
    wareHouseList.value = res.data.map(item => ({
        id: item.id,
        name: item.warehouseName,
        latitude: item.latitude,
        longitude: item.longitude
    }));
    // 先从缓存中查找场库信息
    const cachedWarehouse = uni.getStorageSync('currentWarehouse')
    if (cachedWarehouse && wareHouseList.value.some(w => w.id === cachedWarehouse.id)) {
        currentWarehouse.value = cachedWarehouse
    } else if (wareHouseList.value.length > 0) {
        // 如果缓存中没有，使用场库列表的第一项
        currentWarehouse.value = wareHouseList.value[0]
        uni.setStorageSync('currentWarehouse', currentWarehouse.value)
    }
};

// 初始化套餐数据
const initPackageData = async () => {
    try {
        // 场库不存在，套餐列表为空
        if (!currentWarehouse.value || !currentWarehouse.value.id) {
            packageList.value = [];
            choosePackage.value = {};
            return;
        }

        // 按场库ID获取套餐列表
        const warehouseId = currentWarehouse.value.id;
        const res = await getPackageList({ warehouseId: warehouseId});
        packageList.value = res.data || [];
        
        // 默认选中第一个套餐
        if (packageList.value.length > 0) {
            choosePackage.value = packageList.value[0];
        } 
    } catch (error) {
        uni.showToast({
            title: '套餐数据加载失败',
            icon: 'none'
        });
        packageList.value = [];
        choosePackage.value = {};
    }
};

// 初始化车辆数据
const initCarData = async () => {
    try {
        const res = await getCarList();
        carList.value = res.data || [];
        if(carList.value.length === 0) {
            return;
        }
        
        // 优先使用缓存的车辆
        const cachedCar = uni.getStorageSync('selectedCar');
        if (cachedCar && cachedCar.plateNo) {
            // 检查缓存的车辆是否在当前车辆列表中
            const cachedCarInList = carList.value.find(car => car.plateNo === cachedCar.plateNo);
            if (cachedCarInList) {
                selectedCar.value = cachedCarInList;
                // 更新缓存中的车辆信息（可能有更新）
                try {
                    uni.setStorageSync('selectedCar', cachedCarInList);
                } catch (error) {
                    console.error('更新缓存车辆信息失败:', error);
                }
            } else {
                // 缓存的车辆不在列表中，使用默认逻辑
                selectDefaultCar();
            }
        } else {
            // 没有缓存，使用默认逻辑
            selectDefaultCar();
        }
        
        // 获取用户车辆未过期套餐信息
        if (currentWarehouse.value?.id && selectedCar.value?.plateNo) {
            getUserPackagePlate({ 
                warehouseId: currentWarehouse.value.id, 
                plateNo: selectedCar.value.plateNo,
                vipType: vipType.value
            }).then(res => {
                userPackagePlate.value = res.data || {};
            }).catch(error => {
                console.error('获取用户套餐信息失败:', error);
                userPackagePlate.value = {};
            });
        }
    } catch (error) {
        console.error('初始化车辆数据失败:', error);
        carList.value = [];
        selectedCar.value = {};
        userPackagePlate.value = {};
    }
};

// 选择默认车辆的逻辑
const selectDefaultCar = () => {
    // 设置默认车辆（isDefault为1的车辆）
    const defaultCar = carList.value.find(car => car.isDefault === 1);
    if (defaultCar) {
        selectedCar.value = defaultCar;
    } else if (carList.value.length > 0) {
        // 如果没有默认车辆，选择第一辆
        selectedCar.value = carList.value[0];
    }
    
    // 将选中的车辆缓存起来
    if (selectedCar.value && selectedCar.value.plateNo) {
        try {
            uni.setStorageSync('selectedCar', selectedCar.value);
        } catch (error) {
            console.error('缓存车辆信息失败:', error);
        }
    }
};

// 显示场库选择器
const showWarehouseSelector = () => {
    wareHouseSelector.value = true;
};

// 关闭场库选择器
const closeWarehouseSelector = () => {
    wareHouseSelector.value = false;
};

// 选择场库
const selectWarehouse = (warehouse) => {
    currentWarehouse.value = warehouse;
    uni.setStorageSync("currentWarehouse", warehouse);
    closeWarehouseSelector();
    
    // 切换场库后重新查询套餐列表
    initPackageData();
    
    // 重新获取用户套餐信息
    if (selectedCar.value?.plateNo) {
        getUserPackagePlate({ 
            warehouseId: warehouse.id, 
            plateNo: selectedCar.value.plateNo,
            vipType: vipType.value
        }).then(res => {
            userPackagePlate.value = res.data;
        }).catch(error => {
            console.error('获取用户套餐信息失败:', error);
            userPackagePlate.value = {};
        });
    }
};

// 显示车辆选择器
const openCarSelector = () => {
    if(!hasCar()) {
        return;
    }
    showCarSelector.value = true;
};
// 用户是否添加车辆
const hasCar = () => {
    if (carList.value.length === 0) {
        uni.showToast({
            title: '请先去个人中心添加车辆',
            icon: "none",
            duration: 1500,
        });
        return false;
    }
    return true;
}

// 车辆选择确认
const onCarConfirm = (e) => {
    selectedCar.value = e.value[0];
    showCarSelector.value = false;
    
    // 将选中的车辆缓存起来
    try {
        uni.setStorageSync('selectedCar', selectedCar.value);
    } catch (error) {
        console.error('缓存车辆信息失败:', error);
    }
    
    // 切换车辆后重新获取用户套餐信息
    if (currentWarehouse.value?.id && selectedCar.value?.plateNo) {
        getUserPackagePlate({ 
            warehouseId: currentWarehouse.value.id, 
            plateNo: selectedCar.value.plateNo,
            vipType: vipType.value
        }).then(res => {
            userPackagePlate.value = res.data || {};
            console.log('userPackagePlate', userPackagePlate.value);
        }).catch(error => {
            console.error('获取用户套餐信息失败:', error);
            userPackagePlate.value = {};
        });
    }
};

// 选择套餐
const handleChoosePackage = (item) => {
    choosePackage.value = item;
};

const handleBuyPackage = () => {
    if(!hasCar()) {
        return;
    }
    const packageOrder = {
        warehouseId: currentWarehouse.value.id,
        warehouseName: currentWarehouse.value.name,
        packageId: choosePackage.value.id,
        packageName: choosePackage.value.packageName,
        packagePrice: choosePackage.value.packagePrice,
        packageDays: choosePackage.value.packageType,
        plateNo: selectedCar.value.plateNo,
        vipType: vipType.value,
        isRenewal: !!(userPackagePlate.value?.beginVipTime && userPackagePlate.value?.endVipTime),
        beginVipTime: userPackagePlate.value?.beginVipTime,
        expirationTime: userPackagePlate.value?.endVipTime
    }
    uni.navigateTo({ url: '/pages/package/packageBuy?packageOrder=' + JSON.stringify(packageOrder) });
};

const goToRecord = () => {
    uni.navigateTo({ url: '/pages/package/packageRecord?vipType=' + vipType.value });
};

// 暴露方法给父组件调用
defineExpose({
    initData
});

</script>

<style lang="scss" scoped>
.vip-page {
    min-height: 80vh;
    position: relative;
}

.vip-bg {
    position: absolute;
    top: -15rpx;
    left: 0;
    width: 100%;
    z-index: 1;
}

.vip-card {
    position: relative;
    z-index: 10;
    margin: 15rpx 0 0 50rpx;
    
    .vip-info {
        padding: 24rpx;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        color: #fff;

        .vip-title-container {
            display: flex;
            align-items: center;
            gap: 16rpx;
            cursor: pointer;

            .vip-title {
                font-size: 45rpx;
                font-weight: bold;
            }
        }

        .vip-plate {
            font-size: 30rpx;
            display: flex;
            align-items: center;
            margin:20rpx 0;
            
            .plate-number {
                margin-right: 10rpx;
                font-size: 30rpx;
                font-weight: 500;
                color: #fff;
            }
        }

        .vip-expire {
            font-size: 24rpx;
            color: #ffffff;
            margin-bottom: 10rpx;
        }

        .vip-link {
            margin-top: 15rpx;
            font-size: 30rpx;
            color: #ececec;
        }
    }
}

.package-list {
    position: relative;
    z-index: 10;
    margin-top: 95rpx;
    padding: 0 24rpx;

    .package-title {
        font-size: 30rpx;
        color: #333;
        font-weight: bold;
        margin-bottom: 32rpx;
    }

    .package-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 32rpx;
    }

    .package-item {
        width: calc((100% - 80rpx) / 3);
        background: #fff;
        border-radius: 18rpx;
        height: 160rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        border: 2rpx solid transparent;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;

        .package-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 16rpx 8rpx;

            .package-name {
                font-size: 24rpx;
                color: #333;
                font-weight: 500;
                margin-bottom: 12rpx;
                text-align: center;
            }

            .package-price {
                display: flex;
                align-items: baseline;
                justify-content: center;
                text-align: center;
                
                .price-symbol {
                    font-size: 24rpx;
                    color: #2c2c2c;
                    font-weight: normal;
                }
                
                .price-amount {
                    font-size: 40rpx;
                    color: #232323;
                    font-weight: bold;
                }
            }
        }

        &.active {
            border: 3rpx solid #ffb300;
            background: #fffbe6;
            box-shadow: 0 4rpx 16rpx rgba(255, 179, 0, 0.2);
            transform: scale(1.02);

            .package-content {
                .package-name {
                    color: #ff9900;
                }
                
                .package-price {
                    .price-symbol {
                        color: #ff9900;
                    }
                    
                    .price-amount {
                        color: #ff9900;
                    }
                }
            }
        }
    }

    .buy-btn {
        width: 100%;
        background: linear-gradient(90deg, #4BA1FC 0%, #7e6dff 100%);
        color: #fff;
        font-size: 32rpx;
        font-weight: bold;
        border-radius: 45rpx;
        border: none;
        margin: 40rpx 0 0 0;
    }

    .buy-btn:disabled {
        background: #d0d0d0;
        color: #fff;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 50rpx 0;
    }
}

.buy-section {
    display: none;
}
</style>