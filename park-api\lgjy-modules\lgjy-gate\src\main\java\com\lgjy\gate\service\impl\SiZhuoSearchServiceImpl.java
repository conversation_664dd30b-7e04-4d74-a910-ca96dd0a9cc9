package com.lgjy.gate.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.domain.R;
import com.lgjy.gate.annotation.GateService;
import com.lgjy.gate.config.BrandConfigManager;
import com.lgjy.gate.config.RetryRestTemplate;
import com.lgjy.gate.mapper.GateLogMapper;
import com.lgjy.gate.mapper.ParkingInfoMapper;
import com.lgjy.gate.pojo.CommonPojo;
import com.lgjy.gate.pojo.GateLogPojo;
import com.lgjy.gate.pojo.ParkingInfoPojo;
import com.lgjy.gate.service.GateSearchService;
import com.lgjy.gate.utils.Arith;
import com.lgjy.gate.utils.DateUtils;
import com.lgjy.gate.utils.SiZhuoUtils;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@GateService(brandCode = "sizhuo", serviceType = GateService.Type.SEARCH, description = "思卓停车场查询服务")
public class SiZhuoSearchServiceImpl implements GateSearchService {

    private final RestTemplate restTemplate = RetryRestTemplate.build(3, 100, 5000, 5000);

    @Resource
    private SiZhuoUtils siZhuoUtils;

    @Resource
    private BrandConfigManager brandConfigManager;

    @Resource
    private GateLogMapper gateLogMapper;

    @Resource
    private ParkingInfoMapper parkingInfoMapper;

    /**
     * 查询车辆套餐
     *
     * @param requestParams 请求数据JSON对象
     * @return
     */
    @Override
    public R<String> queryVehicle(JSONObject requestParams) {
        // 从JSONObject中提取参数
        String parkingId = requestParams.getString("parkingId");
        String plateNum = requestParams.getString("plateNum");
        String accessAddress = requestParams.getString("remoteAddr");
        // 获取配置信息
        String physicalId = brandConfigManager.getPhysicalId(parkingId);
        String brandName = brandConfigManager.getSiZhuoParkConfig().getBrandName();
        String baseUrl = brandConfigManager.getSiZhuoParkConfig().getRequestUrl();
        String requestUrl = baseUrl + "/transmit/vehicle/query";

        try {
            long timestamp = System.currentTimeMillis();

            // 生成签名
            StringBuilder signDataBuilder = new StringBuilder();
            signDataBuilder.append("parkingID=").append(physicalId)
                    .append("&plate=").append(plateNum)
                    .append("&timestamp=").append(timestamp);
            String sign = siZhuoUtils.getSignData(physicalId, signDataBuilder);

            // 准备请求数据
            JSONObject requestData = new JSONObject();
            requestData.put("timestamp", timestamp);
            requestData.put("plate", plateNum);
            requestData.put("parkingID", physicalId);
            requestData.put("sign", sign);

            // 2. 发送HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<JSONObject> httpEntity = new HttpEntity<>(requestData, headers);
            JSONObject response = restTemplate.postForObject(requestUrl, httpEntity, JSONObject.class);

            log.info("{}:queryVehicle: 请求路径{},请求数据{},返回数据{}", brandName, requestUrl,requestData,response);

            // 3. 处理响应结果
            Integer responseCode = response.getInteger("code");

            // 4. 记录操作日志
            recordOperationLog(parkingId, plateNum, accessAddress, requestUrl,
                    requestData, response);

            if (responseCode == null) {
                log.error("车辆出场查询结果异常");
            }

            if (responseCode == 1) {
                JSONObject responseData = response.getJSONObject("data");
                JSONObject result = new JSONObject();
                result.put("plateNum", responseData.getString("plate"));
                result.put("beginTime", parseTimestamp(responseData.getString("beginTime")));
                result.put("endTime", parseTimestamp(responseData.getString("endTime")));
                String vehicleType = responseData.getString("vehicleType");
                result.put("vehicleType", isSpecialVehicle(vehicleType) ? 1 : 0);
                return R.ok(result.toJSONString());
            } else{
                return R.fail(response.getString("msg"));
            }

        } catch (Exception e) {
            log.error("queryVehicle failed - parkingId: {}, plateNum: {}",
                    parkingId, plateNum, e);
        }
        return null;
    }

    /**
     * 根据车牌获取缴费金额
     *
     * @param requestParams 请求数据JSON对象
     * @return
     */
    @Override
    public R<String> findParkingRate(JSONObject requestParams) {
        // 从JSONObject中提取参数
        String parkingId = requestParams.getString("parkingId");
        String plateNum = requestParams.getString("plateNum");
        String remoteAddr = requestParams.getString("remoteAddr");
        // 获取配置信息
        String physicalId = brandConfigManager.getPhysicalId(parkingId);
        String brandName = brandConfigManager.getSiZhuoParkConfig().getBrandName();
        String baseUrl = brandConfigManager.getSiZhuoParkConfig().getRequestUrl();
        String requestUrl = baseUrl + "/transmit/price/query";

        // 生成签名
        long timestamp = System.currentTimeMillis();
        StringBuilder signDataBuilder = new StringBuilder();
        signDataBuilder.append("parkingID=").append(physicalId)
                .append("&plate=").append(plateNum)
                .append("&timestamp=").append(timestamp);
        String sign = siZhuoUtils.getSignData(physicalId, signDataBuilder);

        // 构建请求数据
        JSONObject requestData = new JSONObject();
        requestData.put("parkingID", physicalId);
        requestData.put("timestamp", timestamp);
        requestData.put("plate", plateNum);
        requestData.put("sign", sign);

        try {
            // 如果车辆没有入场记录
            List<ParkingInfoPojo> list = parkingInfoMapper.findParkingInfo(parkingId, plateNum);
            if (Collections.isEmpty(list)) {
                return R.fail("车辆不在场");
            }
            // 发送HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<JSONObject> httpEntity = new HttpEntity<>(requestData, headers);
            JSONObject response = restTemplate.postForObject(requestUrl, httpEntity, JSONObject.class);

            log.info("{}:findParkingRate: 请求路径{},请求数据{},返回数据{}", brandName, requestUrl,requestData,response);
            // 记录操作日志
            recordOperationLog(parkingId, plateNum, remoteAddr, requestUrl, requestData, response);

            if (response.get("code").equals(1)) {
                if("0".equals(response.getString("isOnPark"))){
                    return R.fail("车辆不在场或无需支付");
                }
                JSONObject res = new JSONObject();
                res.put("inTime",DateUtils.parseUnixTime(response.getString("inTime")));
                res.put("totalAmount",Arith.fenToYuan(response.getIntValue("totalAmount")));
                res.put("paidAmount",Arith.fenToYuan(response.getIntValue("paidAmount")));
                res.put("discountAmount",Arith.fenToYuan(response.getIntValue("discountAmount")));
                res.put("needPayAmount",Arith.fenToYuan(response.getIntValue("needPayAmount")));
                return R.ok(JSONObject.toJSONString(res));
            } else {
                return R.fail(response.getString("msg"));
            }
        } catch (Exception e) {
            log.error("findParkingRate" + "-Exception", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 道闸出口查询停车费
     * @param requestParams 请求数据JSON对象
     * @return
     */
    @Override
    public R<String> outPayQuery(JSONObject requestParams) {
        // 从JSONObject中提取参数
        String parkingId = requestParams.getString("parkingId");
        Integer channelIndex = requestParams.getInteger("gateNo");
        String remoteAddr = requestParams.getString("remoteAddr");
        // 获取配置信息
        String physicalId = brandConfigManager.getPhysicalId(parkingId);
        String brandName = brandConfigManager.getSiZhuoParkConfig().getBrandName();
        String baseUrl = brandConfigManager.getSiZhuoParkConfig().getRequestUrl();
        String requestUrl = baseUrl + "/transmit/outpay/query";

        long timestamp = System.currentTimeMillis();
        // 生成签名
        StringBuilder signDataBuilder = new StringBuilder();
        signDataBuilder.append("channelIndex=").append(channelIndex)
                .append("&parkingID=").append(physicalId)
                .append("&timestamp=").append(timestamp);
        String sign = siZhuoUtils.getSignData(physicalId, signDataBuilder);
        // 准备请求数据
        JSONObject requestData = new JSONObject();
        requestData.put("parkingID", physicalId);
        requestData.put("timestamp", timestamp);
        requestData.put("channelIndex", channelIndex);
        requestData.put("sign", sign);

        // 发送HTTP请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONObject> httpEntity = new HttpEntity<>(requestData, headers);
        JSONObject response = restTemplate.postForObject(requestUrl, httpEntity, JSONObject.class);

        log.info("{}:outPayQuery: {},{},{}", brandName, requestUrl, requestData, response);
        // 记录操作日志
        recordOperationLog(parkingId, null, remoteAddr, requestUrl, requestData, response);
        if(response.get("code").equals(1)){
            JSONObject result = new JSONObject();
            if(response.get("inTime") == null || response.get("outTime")==null){
                return R.fail("车辆不在场");
            }
            result.put("plateNum",response.get("plate").toString());
            result.put("inTime",response.get("inTime").toString());
            result.put("outTime",response.get("outTime").toString());
            result.put("needPayAmount",Arith.round(Integer.parseInt(response.get("needPayAmount").toString())/100.0,2));
            result.put("totalAmount",Arith.round(Integer.parseInt(response.get("totalAmount").toString())/100.0,2));
            result.put("discountAmount",Arith.round(Integer.parseInt(response.get("discountAmount").toString())/100.0,2));
            result.put("paidAmount",Arith.round(Integer.parseInt(response.get("paidAmount").toString())/100.0,2));
            return R.ok(result.toJSONString());
        }else {
            return R.fail(response.get("msg").toString());
        }
    }

    /**
     * 记录操作日志
     */
    private void recordOperationLog(String parkingId, String plateNum, String remoteAddr,
            String requestUrl, JSONObject requestData, JSONObject response) {

        GateLogPojo logPojo = new GateLogPojo();
        logPojo.setId(UUID.randomUUID().toString().replace("-", ""));
        logPojo.setParkingName("思卓");
        logPojo.setAccessAddress(remoteAddr);
        logPojo.setAddress(requestUrl);
        logPojo.setOperate(0);
        logPojo.setParm(String.format("parkingId:%s&&plateNum:%s", parkingId, plateNum));
        logPojo.setData(requestData.toJSONString());
        logPojo.setResult(response.getInteger("code"));
        logPojo.setResultMsg(Optional.ofNullable(response.getString("msg")).orElse(""));
        logPojo.setLastUpdate(new Date());

        gateLogMapper.gateLogAdd(logPojo);
    }

    /**
     * 异常处理
     */
    private void handleException(CommonPojo commonPojo, Exception e) {
        commonPojo.setResult(false);
        commonPojo.setMsg(e.getMessage());
    }

    /**
     * 解析时间字符串为时间戳（秒）
     */
    private long parseTimestamp(String timeStr) throws ParseException {
        return DateUtils.parseDate(timeStr).getTime() / 1000;
    }

    /**
     * 判断是否为特殊车辆
     */
    private boolean isSpecialVehicle(String vehicleType) {
        return "月租车".equals(vehicleType) || "免费车".equals(vehicleType);
    }

}
