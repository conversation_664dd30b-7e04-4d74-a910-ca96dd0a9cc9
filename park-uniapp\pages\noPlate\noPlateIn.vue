<template>
    <view class="no-plate-in-container">
        <view class="content">
            <view class="content-top">
                <view class="content-top-title">
                    <view class="title"> 无牌车辆入场 </view>
                    <view class="desc"> car register </view>
                </view>
                <image src="/static/image/carLeft.png" class="car-image" mode="aspectFit"></image>
            </view>
        </view>
    </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { getOpenid } from "@/api/login";
import { noPlateIn } from "@/api/parkingOrder";

const gateNo = ref('');
const warehouseId = ref('');
const openid = ref('');
const scanTime = ref(0);

// 页面加载时执行
onLoad((options) => {
    console.log('页面参数:', options);
    
    // 获取二维码原始链接内容
    const qrUrl = decodeURIComponent(options.q || '');
    
    // 获取用户扫码时间 (UNIX时间戳，单位秒)
    const scancode_time = parseInt(options.scancode_time || 0);
    
    console.log('二维码原始链接:', qrUrl);
    console.log('扫码时间:', scancode_time);
    
    if (qrUrl) {
        // 解析URL参数
        parseQrParamsManually(qrUrl);
    } else {
        // 兼容直接传参的方式（开发调试用）
        gateNo.value = options.gateNo || '';
        warehouseId.value = options.warehouseId || '';
        
        if (gateNo.value && warehouseId.value) {
            handleScanParams(gateNo.value, warehouseId.value);
        }
    }
    
    // 保存扫码时间
    scanTime.value = scancode_time;
});

/**
 * 手动解析URL参数方法
 * @param {string} url - 二维码原始URL
 */
const parseQrParamsManually = (url) => {
    try {
        // 提取查询参数部分
        const queryString = url.split('?')[1];
        if (!queryString) {
            throw new Error('URL中没有查询参数');
        }
        
        // 解析参数
        const params = {};
        queryString.split('&').forEach(param => {
            const [key, value] = param.split('=');
            if (key && value) {
                params[decodeURIComponent(key)] = decodeURIComponent(value);
            }
        });
        
        console.log('手动解析的参数:', params);
        
        // 更新页面数据
        gateNo.value = params.gateNo || '';
        warehouseId.value = params.warehouseId || '';
        
        // 调用业务处理方法
        handleScanParams(gateNo.value, warehouseId.value);
        
    } catch (error) {
        console.error('手动解析URL参数也失败:', error);
        uni.showToast({
            title: '二维码格式错误,请重新扫码～',
            icon: 'none',
            duration: 2000
        });
    }
};

/**
 * 处理扫码参数
 * @param {string} gateNoParam - 闸门号
 * @param {string} warehouseIdParam - 场库ID
 */
const handleScanParams = (gateNoParam, warehouseIdParam) => {
    if (gateNoParam && warehouseIdParam) {
        // 参数正确，显示确认弹窗
        setTimeout(() => {
            showConfirmModal();
        }, 500);
    } else {
        uni.showToast({
            title: '参数错误,请重新扫码～',
            icon: 'none',
            duration: 2000
        });
    }
};

const noPlateInPost = () => {
    uni.showLoading({
        title: '入场中...',
        mask: true
    })
    uni.login({
        provider: 'weixin',
        success: (res) => {
            getOpenid({wxCode: res.code}).then(res => {
                openid.value = res.data
                if (!openid.value) {
                    uni.hideLoading()
                    uni.showToast({
                        title: '网络异常,请重新扫码～',
                        icon: 'none',
                        duration: 2000
                    })
                    return
                }
                
                let params = {
                    openId: openid.value,
                    gateNo: gateNo.value,
                    warehouseId: warehouseId.value
                }
                noPlateIn(params).then(res => {
                    uni.hideLoading()
                    if (res.code == 200) {
                        uni.showToast({
                            title: '无牌车入场成功',
                            icon: 'none'
                        })
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none'
                        })
                    }
                }).catch(() => {
                    uni.hideLoading()
                    uni.showToast({
                        title: '入场失败,请重新扫码～',
                        icon: 'none',
                        duration: 2000
                    })
                })
            }).catch(() => {
                uni.hideLoading()
                uni.showToast({
                    title: '网络异常,请重新扫码～',
                    icon: 'none',
                    duration: 2000
                })
            })
        },
        fail: (err) => {
            uni.hideLoading()
            uni.showToast({
                title: '微信登录失败，请重试～',
                icon: 'none',
                duration: 2000
            })
        }
    })
}

// 显示确认弹窗
const showConfirmModal = () => {
    uni.showModal({
        title: '确认入场',
        content: '您确定是无牌车，且要入场吗？',
        cancelText: '取消',
        confirmText: '确认',
        success: (res) => {
            if (res.confirm) {
                noPlateInPost();
            } else if (res.cancel) {
                uni.showToast({
                    title: '已经取消入场',
                    icon: 'none',
                    duration: 2000
                });
            }
        }
    });
};
</script>

<style lang="scss" scoped>
.no-plate-in-container {
    background-color: #f5f5f5;
    height: 100vh;
    padding: 20rpx;
}

.content {
    background: #fff;
    padding: 40rpx;
    border-radius: 20rpx;
    display: flex;
    justify-content: center;
    align-items: center;

    .content-top {
        display: flex;
        align-items: center;

        .content-top-title {
            display: flex;
            flex-direction: column;
            gap: 8rpx;

            .title {
                font-size: 40rpx;
                font-weight: bold;
                color: #212121;
            }

            .desc {
                font-size: 28rpx;
                font-weight: 400;
                color: #9e9e9e;
            }
        }

        .car-image {
            width: 284rpx;
            height: 200rpx;
        }
    }
}
</style>
