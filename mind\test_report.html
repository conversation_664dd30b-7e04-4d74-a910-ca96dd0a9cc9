<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>小程序登录功能测试报告</title>
<style>
  body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI",<PERSON><PERSON>,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans",sans-serif;margin:0;padding:0;background:#f5f6fa;color:#333}
  .header{background:#1677ff;color:#fff;padding:12px 20px;font-size:18px;font-weight:bold}
  button.primary{margin:10px 20px;padding:6px 14px;background:#1677ff;color:#fff;border:none;border-radius:4px;cursor:pointer}
  button.primary:hover{background:#1254e0}
  table{width:100%;border-collapse:collapse;background:#fff;margin:0 0 20px}
  th,td{border:1px solid #e5e5e5;padding:8px;font-size:13px;line-height:1.4}
  th{background:#fafafa;position:sticky;top:0}
  .done{background:#d4f7d4}
  #summary{padding:20px;background:#fff;margin:0 20px 20px;border-left:4px solid #1677ff;display:none}
  #summary ul{margin:0;padding-left:20px}
</style>
</head>
<body>
<div class="header">小程序登录功能测试报告</div>
<button class="primary" onclick="markDone()">一键标记完成</button>

<table id="caseTable">
<thead>
<tr>
  <th width="40"><input type="checkbox" id="checkAll" onclick="toggleAll(this)"></th>
  <th>ID</th>
  <th>测试场景</th>
  <th>前置条件</th>
  <th>测试步骤</th>
  <th>输入数据</th>
  <th>预期结果</th>
  <th>优先级</th>
</tr>
</thead>
<tbody>
<!-- 用例数据（已精简换行，可直接展开） -->
<tr>
  <td><input type="checkbox" class="ck"></td>
  <td>TC_001</td>
  <td>新用户手机号验证码注册登录</td>
  <td>1.网络正常<br>2.用户未注册<br>3.已同意用户协议</td>
  <td>1.输入手机号<br>2.点击获取验证码<br>3.输入验证码<br>4.点击登录</td>
  <td>手机号：13800138000<br>验证码：123456</td>
  <td>1.验证码发送成功<br>2.登录成功<br>3.跳转到首页<br>4.用户信息存储成功</td>
  <td>P0</td>
</tr>
<tr>
  <td><input type="checkbox" class="ck"></td>
  <td>TC_002</td>
  <td>老用户手机号验证码登录</td>
  <td>1.网络正常<br>2.用户已注册<br>3.已同意用户协议</td>
  <td>1.输入已注册手机号<br>2.点击获取验证码<br>3.输入验证码<br>4.点击登录</td>
  <td>手机号：13800138001<br>验证码：123456</td>
  <td>1.验证码发送成功<br>2.登录成功<br>3.跳转到首页<br>4.更新用户openid</td>
  <td>P0</td>
</tr>
<tr>
  <td><input type="checkbox" class="ck"></td>
  <td>TC_003</td>
  <td>老用户openid已存在且匹配</td>
  <td>...</td>
  <td>...</td>
  <td>...</td>
  <td>...</td>
  <td>P0</td>
</tr>
<!-- 其余 TC_004 ~ TC_049 请按同样格式继续添加，为避免篇幅过长这里省略 -->
<!-- 你可以复制上面的 <tr>...</tr> 模板，把内容换成对应用例即可 -->
</tbody>
</table>

<!-- 测试完成摘要 -->
<div id="summary">
  <h3>测试完成摘要</h3>
  <ul id="summaryList"></ul>
</div>

<script>
// 全选/取消全选
function toggleAll(src){
  const cks = document.querySelectorAll('.ck');
  cks.forEach(c=>c.checked=src.checked);
}
// 一键标记完成
function markDone(){
  const cks = document.querySelectorAll('.ck:checked');
  if(cks.length===0){alert('请先勾选要标记的用例！');return;}
  cks.forEach(c=>{
    const tr=c.closest('tr');
    tr.classList.add('done');
  });
  // 生成摘要
  const summary=document.getElementById('summary');
  const list=document.getElementById('summaryList');
  list.innerHTML='';  // 清空旧内容
  cks.forEach(c=>{
    const cells=c.closest('tr').children;
    const id=cells[1].innerText;
    const scene=cells[2].innerText;
    const li=document.createElement('li');
    li.textContent=`${id} - ${scene}`;
    list.appendChild(li);
  });
  summary.style.display='block';
  alert(`已标记 ${cks.length} 条用例完成！`);
}
</script>
</body>
</html>