import  request  from '../utils/request'

// 根据车牌号,场库编号查询订单(小程序停车缴费)
export const getParkingOrderDetail = params => request.post('/wx/parking/order/plateNo', params)

// 查询临停订单列表
export const getParkingOrderList = params => request.post('/wx/parking/order/list', params)

// 停车缴费预下单
export const createParkingOrder = params => request.post('/wx/parking/order/create', params)

// 停车订单支付成功前端回调
export const payParkingOrderCallBack = params => request.post('/wx/parking/order/front/payCallback', params)

// 临停h5跳转小程序支付
export const paymentTemporary = params => request.post('/wx/parking/order/paymentTemporary', params)

// 无牌车入场
export const noPlateIn = params => request.post('/wx/parking/order/noPlateIn', params)

// 无牌车出场
export const noPlateOut = params => request.post('/wx/parking/order/noPlateOut', params)