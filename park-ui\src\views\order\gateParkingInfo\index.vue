<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="车牌号" prop="plateNum">
        <el-input v-model="queryParams.plateNum" placeholder="请输入车牌号" clearable style="width: 200px"
          @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="场库名称" prop="parkingId">
        <el-select v-model="queryParams.parkingId" placeholder="请选择场库" clearable style="width: 200px">
          <el-option v-for="warehouse in warehouseOptions" :key="warehouse.id" :label="warehouse.warehouseName"
            :value="warehouse.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 200px">
          <el-option label="在场" value="0" />
          <el-option label="已出场" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="入场时间" prop="inDate">
        <el-date-picker v-model="inDate" value-format="YYYY-MM-DD" type="date"
          placeholder="选择入场日期" style="width: 200px"></el-date-picker>
      </el-form-item>
      <el-form-item label="出场时间" prop="outDate">
        <el-date-picker v-model="outDate" value-format="YYYY-MM-DD" type="date"
          placeholder="选择出场日期" style="width: 200px"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['order:gateParkingInfo:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['order:gateParkingInfo:edit']">修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['order:gateParkingInfo:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['order:gateParkingInfo:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="gateParkingInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="场库名称" align="center" prop="parkingName" min-width="120">
        <template #default="scope">
          <span>{{ scope.row.parkingName || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="车辆类型" align="center" prop="carType" width="120">
        <template #default="scope">
          <span>{{ scope.row.carType || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="车牌号" align="center" prop="plateNum" width="120">
        <template #default="scope">
          <el-tag :type="getPlateNoTagType(scope.row.plateNum)" :color="getPlateNoColor(scope.row.plateNum)"
            effect="plain" class="plate-tag">
            {{ scope.row.plateNum }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <el-tag :type="scope.row.status === 0 ? 'warning' : 'success'">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="入场时间" align="center" prop="inDateTime" min-width="160">
        <template #default="scope">
          <span v-if="scope.row.inDateTime">{{ parseTime(scope.row.inDateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="出场时间" align="center" prop="outDateTime" min-width="160">
        <template #default="scope">
          <span v-if="scope.row.outDateTime">{{ parseTime(scope.row.outDateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="停车时长" align="center" prop="parkingDurationText" min-width="120">
        <template #default="scope">
          <span>{{ scope.row.parkingDurationText || '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="停车费用" align="center" prop="money" width="100">
        <template #default="scope">
          <span v-if="scope.row.money !== null && scope.row.money !== undefined" class="text-red-500 font-medium">¥{{ scope.row.money }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="支付类型" align="center" prop="payType" min-width="100">
        <template #default="scope">
          <span v-if="scope.row.status === 0 || !scope.row.payType">--</span>
          <span v-else>{{ getPayTypeText(scope.row.payType) }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)"
            v-hasPermi="['order:gateParkingInfo:query']">查看</el-button>
          <!-- <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['order:gateParkingInfo:edit']"
          >修改</el-button> -->
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['order:gateParkingInfo:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <custom-pagination :total="total" v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改车辆出入场记录对话框 -->
    <el-dialog :title="title" v-model="open" :width="isViewMode ? '1200px' : '600px'" append-to-body>
      <!-- 查看模式：使用美化的布局 -->
      <div v-if="isViewMode" class="view-container">
        <el-row :gutter="20">
          <!-- 左侧：详细信息 -->
          <el-col :span="14">
            <el-descriptions title="车辆出入场记录详情" :column="2" border>
              <el-descriptions-item label="场库名称" label-align="right" label-class-name="desc-label">
                {{ form.parkingName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="更新时间" label-align="right" label-class-name="desc-label">
                <span v-if="form.lastUpdate">{{ parseTime(form.lastUpdate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                <span v-else>--</span>
              </el-descriptions-item>
              <el-descriptions-item label="车牌号" label-align="right" label-class-name="desc-label">
                <el-tag v-if="form.plateNum" :type="getPlateNoTagType(form.plateNum)" :color="getPlateNoColor(form.plateNum)" effect="plain" class="plate-tag">
                  {{ form.plateNum }}
                </el-tag>
                <span v-else>--</span>
              </el-descriptions-item>
              <el-descriptions-item label="状态" label-align="right" label-class-name="desc-label">
                <el-tag v-if="form.status !== null && form.status !== undefined" :type="form.status === 0 ? 'warning' : 'success'">
                  {{ getStatusText(form.status) }}
                </el-tag>
                <span v-else>--</span>
              </el-descriptions-item>
              <el-descriptions-item label="车辆类型" label-align="right" label-class-name="desc-label">
                {{ form.carType || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="停车时长" label-align="right" label-class-name="desc-label">
                {{ form.parkingDurationText || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="入场ID" label-align="right" label-class-name="desc-label">
                {{ form.inChannelId || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="出场ID" label-align="right" label-class-name="desc-label">
                {{ form.outChannelId || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="入场通道" label-align="right" label-class-name="desc-label">
                {{ form.inChannelName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="出场通道" label-align="right" label-class-name="desc-label">
                {{ form.outChannelName || '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="入场时间" label-align="right" label-class-name="desc-label">
                {{ form.inDateTime ? parseTime(form.inDateTime, '{y}-{m}-{d} {h}:{i}:{s}') : '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="出场时间" label-align="right" label-class-name="desc-label">
                {{ form.outDateTime ? parseTime(form.outDateTime, '{y}-{m}-{d} {h}:{i}:{s}') : '--' }}
              </el-descriptions-item>
              <el-descriptions-item label="停车费用" label-align="right" label-class-name="desc-label">
                <span v-if="form.money !== null && form.money !== undefined" style="color: #f56c6c; font-weight: bold;">
                  ¥{{ form.money }}
                </span>
                <span v-else>--</span>
              </el-descriptions-item>
              <el-descriptions-item label="支付类型" label-align="right" label-class-name="desc-label">
                <span v-if="form.status === 0 || !form.payType">--</span>
                <span v-else>{{ getPayTypeText(form.payType) }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>

          <!-- 右侧：照片显示 -->
          <el-col :span="10">
            <div class="image-section">
              <h3 style="margin-bottom: 15px; color: #303133;">车辆照片</h3>
              <div class="image-container">
                <div class="image-item" v-if="form.inPic">
                  <h4>入场照片</h4>
                  <el-image :src="form.inPic" fit="cover" style="width: 100%; height: 200px; border-radius: 4px;"
                    :preview-src-list="[form.inPic]" preview-teleported />
                </div>
                <div class="image-item" v-if="form.outPic" style="margin-top: 20px;">
                  <h4>出场照片</h4>
                  <el-image :src="form.outPic" fit="cover" style="width: 100%; height: 200px; border-radius: 4px;"
                    :preview-src-list="[form.outPic]" preview-teleported />
                </div>
                <div v-if="!form.inPic && !form.outPic" class="no-image">
                  <el-empty description="暂无照片" />
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 编辑模式：使用原有的表单布局 -->
      <el-form v-else ref="gateParkingInfoRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="车牌号" prop="plateNum">
              <el-input v-model="form.plateNum" placeholder="请输入车牌号" :disabled="isViewMode" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆类型" prop="carType">
              <el-select v-model="form.carType" placeholder="请选择车辆类型" :disabled="isViewMode">
                <el-option v-for="carType in carTypeOptions" :key="carType" :label="carType" :value="carType" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="场库" prop="parkingId">
              <el-select v-model="form.parkingId" placeholder="请选择场库" :disabled="isViewMode">
                <el-option v-for="warehouse in warehouseOptions" :key="warehouse.id" :label="warehouse.warehouseName"
                  :value="warehouse.id" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="入场通道" prop="inChannelName">
              <el-input v-model="form.inChannelName" placeholder="请输入入场通道" :disabled="isViewMode" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="出场通道" prop="outChannelName">
              <el-input v-model="form.outChannelName" placeholder="请输入出场通道" :disabled="isViewMode" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="停车费用" prop="money">
              <el-input-number v-model="form.money" :precision="2" :min="0" :disabled="isViewMode" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="支付类型" prop="payType">
              <el-select v-model="form.payType" placeholder="请选择支付类型" :disabled="isViewMode || form.status === 0">
                <el-option label="支付宝" :value="1" />
                <el-option label="微信" :value="2" />
                <el-option label="其他" :value="99" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" :disabled="isViewMode" @change="handleStatusChange">
                <el-option label="在场" :value="0" />
                <el-option label="已出场" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">{{ isViewMode ? '关 闭' : '取 消' }}</el-button>
          <el-button type="primary" @click="submitForm" v-if="!isViewMode">确 定</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup name="GateParkingInfo">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue';
import { parseTime } from "@/utils/ruoyi";
import {
  listGateParkingInfo,
  getGateParkingInfo,
  delGateParkingInfo,
  addGateParkingInfo,
  updateGateParkingInfo,
  getCarTypeOptions
} from "@/api/order/gateParkingInfo";
import { optionSelectWarehouse } from "@/api/platform/warehouse";
import CustomPagination from "@/components/CustomPagination/index.vue";

// 注册组件
defineOptions({
  components: {
    CustomPagination,
  },
});

const { proxy } = getCurrentInstance();

const gateParkingInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const inDate = ref('');
const outDate = ref('');
const warehouseOptions = ref([]);
const carTypeOptions = ref([]);
const isViewMode = ref(false);


const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    plateNum: null,
    parkingId: null,
    carType: null,
    status: null,
    inBeginTime: null,
    inEndTime: null,
    outBeginTime: null,
    outEndTime: null
  },
  rules: {
    plateNum: [
      { required: true, message: "车牌号不能为空", trigger: "blur" }
    ],
    carType: [
      { required: true, message: "车辆类型不能为空", trigger: "change" }
    ],
    parkingId: [
      { required: true, message: "场库不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询车辆出入场记录列表 */
function getList() {
  loading.value = true;
  queryParams.value.params = {};

  // 处理入场时间（单日期查询）
  if (inDate.value) {
    queryParams.value.inBeginTime = inDate.value + ' 00:00:00';
    queryParams.value.inEndTime = inDate.value + ' 23:59:59';
  } else {
    queryParams.value.inBeginTime = null;
    queryParams.value.inEndTime = null;
  }

  // 处理出场时间（单日期查询）
  if (outDate.value) {
    queryParams.value.outBeginTime = outDate.value + ' 00:00:00';
    queryParams.value.outEndTime = outDate.value + ' 23:59:59';
  } else {
    queryParams.value.outBeginTime = null;
    queryParams.value.outEndTime = null;
  }

  listGateParkingInfo(queryParams.value).then(response => {
    gateParkingInfoList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  inDate.value = '';
  outDate.value = '';
  proxy.resetForm("queryRef");
  // 重置查询参数到初始状态
  Object.assign(queryParams.value, {
    pageNum: 1,
    pageSize: 10,
    plateNum: null,
    parkingId: null,
    carType: null,
    status: null,
    inBeginTime: null,
    inEndTime: null,
    outBeginTime: null,
    outEndTime: null
  });
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  isViewMode.value = false;
  open.value = true;
  title.value = "添加车辆出入场记录";
}

/** 查看按钮操作 */
function handleView(row) {
  reset();
  isViewMode.value = true;
  const id = row.id || ids.value[0];
  getGateParkingInfo(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "查看车辆出入场记录";
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  isViewMode.value = false;
  const id = row.id || ids.value[0];
  getGateParkingInfo(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改车辆出入场记录";
  });
}



/** 提交按钮 */
function submitForm() {
  proxy.$refs["gateParkingInfoRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateGateParkingInfo(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addGateParkingInfo(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除车辆出入场记录编号为"' + _ids + '"的数据项？').then(function() {
    return delGateParkingInfo(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/gateParkingInfo/export', {
    ...queryParams.value
  }, `车辆出入场记录_${new Date().getTime()}.xlsx`)
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}



// 表单重置
function reset() {
  form.value = {
    id: null,
    parkingId: null,
    plateNum: null,
    carType: null,
    inTime: null,
    inChannelId: null,
    inChannelName: null,
    inPic: null,
    outTime: null,
    outChannelId: null,
    outChannelName: null,
    outPic: null,
    money: null,
    payType: null, // 默认为空，因为新增时默认状态是在场
    status: 0
  };
  proxy.resetForm("gateParkingInfoRef");
}

// 获取车牌号标签类型
function getPlateNoTagType(plateNo) {
  if (!plateNo) return '';
  // 新能源车牌（绿色）
  if (plateNo.length === 8) return 'success';
  // 普通车牌（蓝色）
  return 'primary';
}

// 获取车牌号颜色
function getPlateNoColor(plateNo) {
  if (!plateNo) return '#909399';
  // 新能源车牌：浅绿色，普通车牌：浅蓝色
  return plateNo.length === 8 ? '#d4edda' : '#cce7ff';
}

// 获取支付类型文本
function getPayTypeText(payType) {
  if (!payType || payType === 'null' || payType === 'undefined') {
    return '--';
  }

  switch (String(payType)) {
    case '1':
      return '支付宝';
    case '2':
      return '微信';
    case '3':
      return '其他';
    default:
      return '--';
  }
}

// 获取状态文本
function getStatusText(status) {
  if (status === null || status === undefined) {
    return '--';
  }

  switch (Number(status)) {
    case 0:
      return '在场';
    case 1:
      return '出场';
    default:
      return '--';
  }
}

// 状态变化处理
function handleStatusChange(status) {
  if (status === 0) {
    // 如果状态改为在场，清空支付类型
    form.value.payType = null;
  }
}

// 初始化数据
async function initData() {
  try {
    // 获取场库选项
    const warehouseRes = await optionSelectWarehouse();
    warehouseOptions.value = warehouseRes.data || [];

    // 获取车辆类型选项
    const carTypeRes = await getCarTypeOptions();
    carTypeOptions.value = carTypeRes.data || [];
  } catch (error) {
    console.error('初始化数据失败:', error);
  }
}

onMounted(() => {
  getList();
  initData();
});
</script>

<style scoped>
/* 查看模式容器样式 */
.view-container {
  padding: 20px;
}

/* 图片区域样式 */
.image-section {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.image-section h3 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.image-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.image-item h4 {
  margin: 0 0 10px 0;
  color: #606266;
  font-weight: 500;
  font-size: 14px;
}

.no-image {
  text-align: center;
  padding: 40px 0;
  background: #fff;
  border-radius: 4px;
  border: 1px dashed #dcdfe6;
}

/* 描述列表样式优化 */
:deep(.el-descriptions__title) {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
}

:deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell) {
  padding: 12px 16px;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #606266;
  width: 90px !important;
  min-width: 90px !important;
}

:deep(.el-descriptions__content) {
  color: #303133;
}

/* 标签列宽度统一 */
:deep(.desc-label) {
  width: 90px !important;
  min-width: 90px !important;
  max-width: 90px !important;
  text-align: right !important;
}

/* 强制描述列表标签列宽度一致 */
:deep(.el-descriptions .el-descriptions__label.desc-label) {
  width: 90px !important;
  min-width: 90px !important;
  max-width: 90px !important;
  text-align: right !important;
  box-sizing: border-box !important;
}

:deep(.el-descriptions__body .el-descriptions__table .el-descriptions__cell.el-descriptions__label) {
  width: 90px !important;
  min-width: 90px !important;
  max-width: 90px !important;
  text-align: right !important;
}

/* 车牌号标签椭圆形样式 */
:deep(.plate-tag) {
  border-radius: 12px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

/* 通用样式 */
.text-gray-400 {
  color: #9ca3af;
}

.text-red-500 {
  color: #ef4444;
}

.font-medium {
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .view-container .el-col:first-child {
    margin-bottom: 20px;
  }
}
</style>

